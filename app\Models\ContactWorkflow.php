<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ContactWorkflow extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the user who created this workflow
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get all workflow assignments
     */
    public function assignments()
    {
        return $this->hasMany(ContactWorkflowAssignment::class, 'workflow_id');
    }

    /**
     * Get leads assigned to this workflow
     */
    public function leads()
    {
        return $this->hasManyThrough(
            Lead::class,
            ContactWorkflowAssignment::class,
            'workflow_id',
            'id',
            'id',
            'contact_id'
        )->where('contact_type', 'lead');
    }

    /**
     * Get deals assigned to this workflow
     */
    public function deals()
    {
        return $this->hasManyThrough(
            Deal::class,
            ContactWorkflowAssignment::class,
            'workflow_id',
            'id',
            'id',
            'contact_id'
        )->where('contact_type', 'deal');
    }

    /**
     * Assign a contact to this workflow
     */
    public function assignContact($contactId, $contactType, $notes = null)
    {
        return ContactWorkflowAssignment::firstOrCreate([
            'workflow_id' => $this->id,
            'contact_id' => $contactId,
            'contact_type' => $contactType,
        ], [
            'notes' => $notes,
            'assigned_by' => auth()->id(),
        ]);
    }

    /**
     * Remove a contact from this workflow
     */
    public function removeContact($contactId, $contactType)
    {
        return ContactWorkflowAssignment::where([
            'workflow_id' => $this->id,
            'contact_id' => $contactId,
            'contact_type' => $contactType,
        ])->delete();
    }

    /**
     * Check if a contact is assigned to this workflow
     */
    public function hasContact($contactId, $contactType)
    {
        return ContactWorkflowAssignment::where([
            'workflow_id' => $this->id,
            'contact_id' => $contactId,
            'contact_type' => $contactType,
        ])->exists();
    }

    /**
     * Get contact count for this workflow
     */
    public function getContactCountAttribute()
    {
        return $this->assignments()->count();
    }

    /**
     * Scope to get active workflows
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
