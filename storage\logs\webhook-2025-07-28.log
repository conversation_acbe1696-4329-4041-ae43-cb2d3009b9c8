[2025-07-28 05:07:16] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-28T05:07:16.657880Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":10,"status":"dispatching"} 
[2025-07-28 05:07:19] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T05:07:19.040515Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2342.0,"user_id":84,"entity_id":10,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-28T05:07:16.698262Z","data":{"event_id":5,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["{{calendar_events.enter_your_business_name}}"],"custom_fields_value":["sit"],"updated_at":"2025-07-28T05:07:16.000000Z","created_at":"2025-07-28T05:07:16.000000Z","id":10,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["{{calendar_events.enter_your_business_name}}"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 05:07:19] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-28T05:07:19.041641Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 05:25:29] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-28T05:25:29.478308Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-28 05:25:33] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2020 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T05:25:33.750019Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":4266.0,"user_id":84,"entity_id":6,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-28T05:25:29.483575Z","data":{"id":6,"title":"OMX Flow Live","start_date":"2025-07-28 05:25:10","end_date":"2026-07-28 05:25:10","duration":60,"booking_per_slot":1,"minimum_notice":44,"description":"OK","location":"phone","meet_link":"8617555736","physical_address":null,"custom_fields":[{"unique_key":"{{calendar_events.enter_gst_number}}","type":"number","label":"Enter GST NUmber","options":[],"is_required":0},{"unique_key":"{{calendar_events.who_are_your_key_competitors_and_what_differentiates_you}}","type":"textarea","label":"Who are your key competitors, and what differentiates you?","options":[],"is_required":0},{"unique_key":"{{calendar_events.enter_your_business_name}}","type":"text","label":"Enter Your Business Name","options":[],"is_required":0}],"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2020 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 05:25:33] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 1 {"timestamp":"2025-07-28T05:25:33.750533Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2020 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 05:28:05] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-28T05:28:05.026778Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":11,"status":"dispatching"} 
[2025-07-28 05:28:07] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T05:28:07.126975Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2084.0,"user_id":84,"entity_id":11,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-28T05:28:05.042975Z","data":{"event_id":6,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"14:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-28T05:28:04.000000Z","created_at":"2025-07-28T05:28:04.000000Z","id":11,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"14:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 05:28:07] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-28T05:28:07.127408Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 05:32:53] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-28T05:32:53.009288Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-28 05:32:55] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T05:32:55.103649Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2090.0,"user_id":84,"entity_id":7,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-28T05:32:53.014132Z","data":{"id":7,"title":"Live Chat Bot AI Live","start_date":"2025-07-28 05:32:42","end_date":"2026-07-28 05:32:42","duration":60,"booking_per_slot":1,"minimum_notice":29,"description":"OK This is a Best","location":"phone","meet_link":"Siliguri","physical_address":null,"custom_fields":[{"unique_key":"{{calendar_events.enter_your_business_name}}","type":"text","label":"Enter Your Business Name","options":[],"is_required":0},{"unique_key":"{{calendar_events.enter_gst_number}}","type":"number","label":"Enter GST NUmber","options":[],"is_required":0},{"unique_key":"{{calendar_events.who_are_your_key_competitors_and_what_differentiates_you}}","type":"textarea","label":"Who are your key competitors, and what differentiates you?","options":[],"is_required":0}],"date_override":"[\"2025-07-29T04:30\"]","created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 05:32:55] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 1 {"timestamp":"2025-07-28T05:32:55.104057Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 05:34:04] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-28T05:34:04.067008Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":12,"status":"dispatching"} 
[2025-07-28 05:34:06] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T05:34:06.123302Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2048.0,"user_id":84,"entity_id":12,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-28T05:34:04.074889Z","data":{"event_id":7,"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-30","time":"13:00","selected_location":{"type":"phone","value":"Siliguri","display":"Phone call"},"custom_fields":["{{calendar_events.enter_your_business_name}}"],"custom_fields_value":["Smart Internz"],"updated_at":"2025-07-28T05:34:04.000000Z","created_at":"2025-07-28T05:34:04.000000Z","id":12,"form_data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-30","time":"13:00","selected_location":{"type":"phone","value":"Siliguri","display":"Phone call"},"custom_fields":["{{calendar_events.enter_your_business_name}}"],"custom_fields_value":["Smart Internz"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 05:34:06] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-28T05:34:06.124093Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 06:08:31] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-28T06:08:31.509498Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-28 06:08:33] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T06:08:33.612881Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2097.0,"user_id":84,"entity_id":8,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-28T06:08:31.515922Z","data":{"id":8,"title":"This is the best","start_date":"2025-07-28 06:08:17","end_date":"2026-07-28 06:08:17","duration":60,"booking_per_slot":1,"minimum_notice":39,"description":"<p>OK, This is the Best&nbsp; &nbsp; sdkbknknlafelkalk</p>","location":"phone","meet_link":"8617555736","physical_address":null,"custom_fields":[{"unique_key":"{{calendar_events.enter_gst_number}}","type":"number","label":"Enter GST NUmber","options":[],"is_required":true}],"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 06:08:33] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 1 {"timestamp":"2025-07-28T06:08:33.614214Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-28 06:11:15] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-28T06:11:15.291445Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":13,"status":"dispatching"} 
[2025-07-28 06:11:17] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-28T06:11:17.363755Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2066.0,"user_id":84,"entity_id":13,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-28T06:11:15.298130Z","data":{"event_id":8,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"13:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["{{calendar_events.enter_gst_number}}"],"custom_fields_value":["89654235689"],"updated_at":"2025-07-28T06:11:15.000000Z","created_at":"2025-07-28T06:11:15.000000Z","id":13,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"13:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["{{calendar_events.enter_gst_number}}"],"custom_fields_value":["89654235689"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-28 06:11:17] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-28T06:11:17.365017Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
