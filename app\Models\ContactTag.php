<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ContactTag extends Model
{
    use HasFactory;

    protected $fillable = [
        'contact_id',
        'contact_type',
        'label_id',
        'added_by',
    ];

    /**
     * Get the label/tag
     */
    public function label()
    {
        return $this->belongsTo(Label::class, 'label_id');
    }

    /**
     * Get the user who added this tag
     */
    public function addedBy()
    {
        return $this->belongsTo(User::class, 'added_by');
    }

    /**
     * Get the contact (polymorphic relationship)
     */
    public function contact()
    {
        if ($this->contact_type === 'lead') {
            return $this->belongsTo(Lead::class, 'contact_id');
        } elseif ($this->contact_type === 'deal') {
            return $this->belongsTo(Deal::class, 'contact_id');
        }
        
        return null;
    }

    /**
     * Get the lead if contact type is lead
     */
    public function lead()
    {
        return $this->belongsTo(Lead::class, 'contact_id');
    }

    /**
     * Get the deal if contact type is deal
     */
    public function deal()
    {
        return $this->belongsTo(Deal::class, 'contact_id');
    }

    /**
     * Scope to filter by contact type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('contact_type', $type);
    }

    /**
     * Get contact name based on type
     */
    public function getContactNameAttribute()
    {
        if ($this->contact_type === 'lead' && $this->lead) {
            return $this->lead->name;
        } elseif ($this->contact_type === 'deal' && $this->deal) {
            return $this->deal->name;
        }
        
        return 'Unknown Contact';
    }

    /**
     * Get tag name
     */
    public function getTagNameAttribute()
    {
        return $this->label ? $this->label->name : 'Unknown Tag';
    }

    /**
     * Get tag color
     */
    public function getTagColorAttribute()
    {
        return $this->label ? $this->label->color : 'secondary';
    }
}
