<?php

namespace App\Exports;

use App\Models\Lead;
use App\Models\Deal;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ContactExport implements FromCollection, WithHeadings, WithMapping
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $contacts = collect();
        
        // Get leads
        $leads = Lead::where('created_by', Auth::user()->creatorId())
            ->with(['pipeline', 'stage'])
            ->get();
            
        foreach ($leads as $lead) {
            $contacts->push([
                'id' => $lead->id,
                'name' => $lead->name,
                'email' => $lead->email,
                'phone' => $lead->phone,
                'type' => 'Lead',
                'pipeline' => $lead->pipeline ? $lead->pipeline->name : '',
                'stage' => $lead->stage ? $lead->stage->name : '',
                'subject' => $lead->subject,
                'created_at' => $lead->created_at,
            ]);
        }
        
        // Get deals
        $deals = Deal::where('created_by', Auth::user()->creatorId())
            ->with(['pipeline', 'stage'])
            ->get();
            
        foreach ($deals as $deal) {
            $contacts->push([
                'id' => $deal->id,
                'name' => $deal->name,
                'email' => '', // Deals don't have email
                'phone' => $deal->phone ?? '',
                'type' => 'Deal',
                'pipeline' => $deal->pipeline ? $deal->pipeline->name : '',
                'stage' => $deal->stage ? $deal->stage->name : '',
                'subject' => '', // Deals don't have subject
                'created_at' => $deal->created_at,
            ]);
        }
        
        return $contacts;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Email',
            'Phone',
            'Type',
            'Pipeline',
            'Stage',
            'Subject',
            'Created At',
        ];
    }

    /**
     * @param mixed $contact
     * @return array
     */
    public function map($contact): array
    {
        return [
            $contact['id'],
            $contact['name'],
            $contact['email'],
            $contact['phone'],
            $contact['type'],
            $contact['pipeline'],
            $contact['stage'],
            $contact['subject'],
            $contact['created_at'] ? $contact['created_at']->format('Y-m-d H:i:s') : '',
        ];
    }
}
