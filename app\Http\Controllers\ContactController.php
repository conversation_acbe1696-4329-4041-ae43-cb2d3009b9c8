<?php

namespace App\Http\Controllers;

use App\Models\Lead;
use App\Models\Deal;
use App\Models\ContactGroup;
use App\Models\ContactGroupMember;
use App\Models\ContactWorkflow;
use App\Models\ContactWorkflowAssignment;
use App\Models\ContactTag;
use App\Models\Label;
use App\Models\Pipeline;
use App\Models\Stage;
use App\Models\CalendarEvent;
use App\Models\AppointmentBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ContactExport;
use App\Imports\ContactImport;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ContactController extends Controller
{
    /**
     * Display a listing of contacts from leads and deals.
     */
    public function index(Request $request)
    {
        $contacts = [];

        // Build leads query
        $leadsQuery = Lead::where('created_by', Auth::user()->creatorId());

        // Apply filters for leads
        if ($request->filled('filter_name')) {
            $leadsQuery->where('name', 'like', '%' . $request->filter_name . '%');
        }
        if ($request->filled('filter_email')) {
            $leadsQuery->where('email', 'like', '%' . $request->filter_email . '%');
        }
        if ($request->filled('filter_phone')) {
            $leadsQuery->where('phone', 'like', '%' . $request->filter_phone . '%');
        }
        if ($request->filled('filter_pipeline')) {
            $leadsQuery->where('pipeline_id', $request->filter_pipeline);
        }
        if ($request->filled('filter_stage')) {
            $leadsQuery->where('stage_id', $request->filter_stage);
        }
        if ($request->filled('filter_date_from')) {
            $leadsQuery->whereDate('created_at', '>=', $request->filter_date_from);
        }
        if ($request->filled('filter_date_to')) {
            $leadsQuery->whereDate('created_at', '<=', $request->filter_date_to);
        }

        // Apply sorting for leads
        $this->applySorting($leadsQuery, $request->filter_sort ?? 'name_asc');

        // Fetch leads data
        $leads = $leadsQuery->get();
        foreach ($leads as $lead) {
            $contacts[] = [
                'id' => $lead->id,
                'name' => $lead->name,
                'email' => $lead->email ?? '',
                'phone' => $lead->phone ?? '',
                'type' => 'Lead'
            ];
        }

        // Build deals query
        $dealsQuery = Deal::where('created_by', Auth::user()->creatorId());

        // Apply filters for deals
        if ($request->filled('filter_name')) {
            $dealsQuery->where('name', 'like', '%' . $request->filter_name . '%');
        }
        if ($request->filled('filter_phone')) {
            $dealsQuery->where('phone', 'like', '%' . $request->filter_phone . '%');
        }
        if ($request->filled('filter_pipeline')) {
            $dealsQuery->where('pipeline_id', $request->filter_pipeline);
        }
        if ($request->filled('filter_stage')) {
            $dealsQuery->where('stage_id', $request->filter_stage);
        }
        if ($request->filled('filter_date_from')) {
            $dealsQuery->whereDate('created_at', '>=', $request->filter_date_from);
        }
        if ($request->filled('filter_date_to')) {
            $dealsQuery->whereDate('created_at', '<=', $request->filter_date_to);
        }

        // Apply sorting for deals
        $this->applySorting($dealsQuery, $request->filter_sort ?? 'name_asc');

        // Fetch deals data
        $deals = $dealsQuery->get();
        foreach ($deals as $deal) {
            $contacts[] = [
                'id' => $deal->id,
                'name' => $deal->name ?? 'No Name',
                'email' => '', // Deals don't have email field
                'phone' => $deal->phone ?? '',
                'type' => 'Deal'
            ];
        }

        // External contacts are now loaded via JavaScript on the frontend
        // This provides better performance and handles API connectivity issues gracefully

        // Apply type filter (only for internal contacts - leads and deals)
        if ($request->filled('filter_type') && $request->filter_type !== 'WhatsApp') {
            $contacts = array_filter($contacts, function($contact) use ($request) {
                return $contact['type'] === $request->filter_type;
            });
        }

        // Get enabled module integration for OMX Flow (same as leads)
        $omxFlowModule = \App\Models\ModuleIntegration::enabled()
            ->whereNotNull('base_url')
            ->where('base_url', '!=', '')
            ->first();

        // Get pipelines for convert to lead modal
        $pipelines = \App\Models\Pipeline::where('created_by', Auth::user()->creatorId())->get();

        return view('contacts.index', compact('contacts', 'omxFlowModule', 'pipelines'));
    }

    /**
     * Check which external contact emails have already been converted to leads
     */
    public function checkConvertedContacts(Request $request)
    {
        $emails = $request->input('emails', []);

        if (empty($emails)) {
            return response()->json(['converted_emails' => []]);
        }

        // Check if any of these emails already exist as leads
        $convertedEmails = Lead::where('created_by', Auth::user()->creatorId())
            ->whereIn('email', $emails)
            ->pluck('email')
            ->map(function($email) {
                return strtolower($email);
            })
            ->toArray();

        return response()->json(['converted_emails' => $convertedEmails]);
    }

    /**
     * Get stages for a specific pipeline
     */
    public function getPipelineStages(Request $request)
    {
        $pipelineId = $request->input('pipeline_id');

        \Log::info('Getting stages for pipeline:', ['pipeline_id' => $pipelineId, 'user_creator_id' => Auth::user()->creatorId()]);

        if (!$pipelineId) {
            return response()->json(['stages' => []]);
        }

        $stages = \App\Models\LeadStage::where('pipeline_id', $pipelineId)
            ->where('created_by', Auth::user()->creatorId())
            ->orderBy('order', 'asc')
            ->orderBy('id', 'asc')
            ->get(['id', 'name']);

        \Log::info('Found stages:', ['count' => $stages->count(), 'stages' => $stages->toArray()]);

        return response()->json(['stages' => $stages]);
    }

    /**
     * Fetch external contacts from API
     */
    private function fetchExternalContacts(Request $request)
    {
        $externalContacts = [];

        try {
            // Generate SSO token (similar to leads list view)
            $ssoToken = $this->generateSsoToken();

            if ($ssoToken) {
                $baseUrl = config('app.url');
                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $ssoToken,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'X-User-Email' => Auth::user()->email
                ])->get($baseUrl . '/api/sso/contacts/simple-list');

                if ($response->successful()) {
                    $data = $response->json();

                    if (isset($data['data']['contacts']) && is_array($data['data']['contacts'])) {
                        foreach ($data['data']['contacts'] as $contact) {
                            $contactName = $contact['full_name'] ?? $contact['name'] ?? 'N/A';
                            $contactEmail = $contact['email'] ?? '';

                            // Apply filters for external contacts
                            $includeContact = true;

                            if ($request->filled('filter_name') && stripos($contactName, $request->filter_name) === false) {
                                $includeContact = false;
                            }

                            if ($request->filled('filter_email') && stripos($contactEmail, $request->filter_email) === false) {
                                $includeContact = false;
                            }

                            if ($includeContact) {
                                $externalContacts[] = [
                                    'id' => 'ext_' . $contact['id'],
                                    'name' => $contactName,
                                    'email' => $contactEmail,
                                    'phone' => '', // External contacts might not have phone
                                    'type' => 'WhatsApp',
                                    'source' => 'external',
                                    'source_id' => $contact['id'],
                                    'external_id' => $contact['id']
                                ];
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // Log error but don't break the page
            Log::error('Failed to fetch external contacts: ' . $e->getMessage());
        }

        return $externalContacts;
    }

    /**
     * Generate SSO token for external API
     */
    private function generateSsoToken()
    {
        try {
            $baseUrl = config('app.url');
            $response = Http::post($baseUrl . '/api/sso/generate-token', [
                'email' => Auth::user()->email,
                'name' => Auth::user()->name
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['token'] ?? null;
            }
        } catch (\Exception $e) {
            Log::error('Failed to generate SSO token: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Update lead via AJAX
     */
    public function updateLead(Request $request, $id)
    {
        $lead = Lead::where('id', $id)
            ->where('created_by', Auth::user()->creatorId())
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        $validator = \Validator::make($request->all(), [
            'name' => 'required|max:120',
            'email' => 'nullable|email',
            'phone' => 'nullable|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $lead->name = $request->name;
        $lead->email = $request->email;
        $lead->phone = $request->phone;
        $lead->save();

        return response()->json(['success' => 'Lead updated successfully']);
    }

    /**
     * Update deal via AJAX
     */
    public function updateDeal(Request $request, $id)
    {
        $deal = Deal::where('id', $id)
            ->where('created_by', Auth::user()->creatorId())
            ->first();

        if (!$deal) {
            return response()->json(['error' => 'Deal not found'], 404);
        }

        $validator = \Validator::make($request->all(), [
            'name' => 'required|max:120',
            'phone' => 'nullable|max:20',
            'price' => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $deal->name = $request->name;
        $deal->phone = $request->phone;
        $deal->price = $request->price ?? 0;
        $deal->save();

        return response()->json(['success' => 'Deal updated successfully']);
    }

    /**
     * Store a new contact (Lead or Deal)
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'contact_type' => 'required|in:lead,deal',
            'name' => 'required|max:120',
            'email' => 'nullable|email',
            'phone' => 'nullable|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            if ($request->contact_type === 'lead') {
                $validator = Validator::make($request->all(), [
                    'pipeline_id' => 'required|exists:pipelines,id',
                    'stage_id' => 'required|exists:stages,id',
                ]);

                if ($validator->fails()) {
                    return response()->json(['errors' => $validator->errors()], 422);
                }

                $lead = Lead::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'phone' => $request->phone,
                    'subject' => $request->subject ?? 'New Lead',
                    'pipeline_id' => $request->pipeline_id,
                    'stage_id' => $request->stage_id,
                    'user_id' => Auth::user()->id,
                    'created_by' => Auth::user()->creatorId(),
                ]);

                return response()->json([
                    'success' => true,
                    'message' => __('Lead created successfully'),
                    'contact' => $lead
                ]);

            } elseif ($request->contact_type === 'deal') {
                $validator = Validator::make($request->all(), [
                    'deal_pipeline_id' => 'required|exists:pipelines,id',
                    'deal_stage_id' => 'required|exists:stages,id',
                ]);

                if ($validator->fails()) {
                    return response()->json(['errors' => $validator->errors()], 422);
                }

                $deal = Deal::create([
                    'name' => $request->name,
                    'phone' => $request->phone,
                    'price' => $request->price ?? 0,
                    'pipeline_id' => $request->deal_pipeline_id,
                    'stage_id' => $request->deal_stage_id,
                    'created_by' => Auth::user()->creatorId(),
                ]);

                return response()->json([
                    'success' => true,
                    'message' => __('Deal created successfully'),
                    'contact' => $deal
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('An error occurred while creating the contact')
            ], 500);
        }
    }

    /**
     * Get contact groups for API
     */
    public function getContactGroups()
    {
        try {
            $groups = ContactGroup::where('created_by', Auth::user()->creatorId())
                ->select('id', 'name', 'description')
                ->get();

            return response()->json(['groups' => $groups]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to load contact groups'], 500);
        }
    }

    /**
     * Get workflows for API
     */
    public function getWorkflows()
    {
        try {
            $workflows = ContactWorkflow::where('created_by', Auth::user()->creatorId())
                ->where('is_active', true)
                ->select('id', 'name', 'description')
                ->get();

            return response()->json(['workflows' => $workflows]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to load workflows'], 500);
        }
    }

    /**
     * Get contact tags for API
     */
    public function getContactTags()
    {
        try {
            $tags = Label::where('created_by', Auth::user()->creatorId())
                ->select('id', 'name', 'color')
                ->get();

            return response()->json(['tags' => $tags]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to load tags'], 500);
        }
    }

    /**
     * Get contacts list for API
     */
    public function getContactsList()
    {
        try {
            $contacts = [];

            // Get leads
            $leads = Lead::where('created_by', Auth::user()->creatorId())
                ->select('id', 'name', 'email')
                ->get();

            foreach ($leads as $lead) {
                $contacts[] = [
                    'id' => $lead->id,
                    'name' => $lead->name,
                    'email' => $lead->email,
                    'type' => 'lead'
                ];
            }

            // Get deals
            $deals = Deal::where('created_by', Auth::user()->creatorId())
                ->select('id', 'name')
                ->get();

            foreach ($deals as $deal) {
                $contacts[] = [
                    'id' => $deal->id,
                    'name' => $deal->name,
                    'email' => '',
                    'type' => 'deal'
                ];
            }

            return response()->json(['contacts' => $contacts]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to load contacts'], 500);
        }
    }

    /**
     * Add contacts to group
     */
    public function addToGroup(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'group_id' => 'nullable|exists:contact_groups,id',
            'new_group_name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Create new group if specified
            if ($request->new_group_name) {
                $group = ContactGroup::create([
                    'name' => $request->new_group_name,
                    'created_by' => Auth::user()->creatorId(),
                ]);
                $groupId = $group->id;
            } else {
                $groupId = $request->group_id;
            }

            if (!$groupId) {
                return response()->json(['error' => 'Group is required'], 422);
            }

            // Add selected contacts to group (this would be handled by JavaScript)
            return response()->json([
                'success' => true,
                'message' => __('Contacts added to group successfully')
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to add contacts to group'], 500);
        }
    }

    /**
     * Add contacts to workflow
     */
    public function addToWorkflow(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'workflow_id' => 'required|exists:contact_workflows,id',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Add selected contacts to workflow (this would be handled by JavaScript)
            return response()->json([
                'success' => true,
                'message' => __('Contacts added to workflow successfully')
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to add contacts to workflow'], 500);
        }
    }

    /**
     * Add tags to contacts
     */
    public function addTags(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tags' => 'required|array',
            'tags.*' => 'string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Process tags and add to selected contacts (this would be handled by JavaScript)
            return response()->json([
                'success' => true,
                'message' => __('Tags added successfully')
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to add tags'], 500);
        }
    }

    /**
     * Book appointment
     */
    public function bookAppointment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'contact_id' => 'required',
            'appointment_date' => 'required|date',
            'appointment_time' => 'required',
            'duration' => 'required|integer|min:15',
            'title' => 'required|string|max:255',
            'location' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Create calendar event for appointment
            $startDateTime = $request->appointment_date . ' ' . $request->appointment_time;

            // This would integrate with existing appointment booking system
            return response()->json([
                'success' => true,
                'message' => __('Appointment booked successfully')
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to book appointment'], 500);
        }
    }

    /**
     * Export contacts
     */
    public function export()
    {
        try {
            $name = 'contacts_' . date('Y-m-d_H-i-s');
            return Excel::download(new ContactExport(), $name . '.xlsx');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', __('Failed to export contacts'));
        }
    }

    /**
     * Download sample import file
     */
    public function sampleImport()
    {
        try {
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="contacts_sample.csv"',
            ];

            $sampleData = [
                ['Name', 'Email', 'Phone', 'Type', 'Pipeline', 'Stage'],
                ['John Doe', '<EMAIL>', '+1234567890', 'Lead', 'Sales Pipeline', 'New'],
                ['Jane Smith', '<EMAIL>', '+0987654321', 'Deal', 'Sales Pipeline', 'Qualified'],
            ];

            $callback = function() use ($sampleData) {
                $file = fopen('php://output', 'w');
                foreach ($sampleData as $row) {
                    fputcsv($file, $row);
                }
                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', __('Failed to download sample file'));
        }
    }

    /**
     * Bulk import contacts
     */
    public function bulkImport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'import_file' => 'required|file|mimes:csv,txt|max:5120', // 5MB max
            'skip_duplicates' => 'boolean',
            'default_pipeline' => 'nullable|exists:pipelines,id',
            'default_stage' => 'nullable|exists:stages,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $skipDuplicates = $request->boolean('skip_duplicates', true);
            $defaultPipelineId = $request->default_pipeline;
            $defaultStageId = $request->default_stage;

            // Create import instance
            $import = new ContactImport($skipDuplicates, $defaultPipelineId, $defaultStageId);

            // Process the CSV file
            Excel::import($import, $request->file('import_file'));

            // Get results
            $results = $import->getResults();

            return response()->json([
                'success' => true,
                'message' => __('Import completed successfully'),
                'imported' => $results['imported'],
                'skipped' => $results['skipped'],
                'errors' => $results['errors'],
                'error_messages' => $results['error_messages']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to import contacts: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Apply sorting to query
     */
    private function applySorting($query, $sortOption)
    {
        switch ($sortOption) {
            case 'name_desc':
                $query->orderBy('name', 'desc');
                break;
            case 'created_desc':
                $query->orderBy('created_at', 'desc');
                break;
            case 'created_asc':
                $query->orderBy('created_at', 'asc');
                break;
            case 'email_asc':
                $query->orderBy('email', 'asc');
                break;
            case 'name_asc':
            default:
                $query->orderBy('name', 'asc');
                break;
        }
    }
}
