<?php

namespace App\Imports;

use App\Models\Lead;
use App\Models\Deal;
use App\Models\Pipeline;
use App\Models\Stage;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ContactImport implements ToCollection, WithHeadingRow
{
    protected $results = [
        'imported' => 0,
        'skipped' => 0,
        'errors' => 0,
        'error_messages' => []
    ];

    protected $skipDuplicates;
    protected $defaultPipelineId;
    protected $defaultStageId;

    public function __construct($skipDuplicates = true, $defaultPipelineId = null, $defaultStageId = null)
    {
        $this->skipDuplicates = $skipDuplicates;
        $this->defaultPipelineId = $defaultPipelineId;
        $this->defaultStageId = $defaultStageId;
    }

    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection)
    {
        foreach ($collection as $row) {
            $this->processRow($row);
        }
    }

    protected function processRow($row)
    {
        // Validate required fields
        $validator = Validator::make($row->toArray(), [
            'name' => 'required|string|max:255',
            'email' => 'nullable|email',
            'phone' => 'nullable|string|max:20',
            'type' => 'nullable|in:Lead,Deal,lead,deal',
        ]);

        if ($validator->fails()) {
            $this->results['errors']++;
            $this->results['error_messages'][] = "Row with name '{$row['name']}': " . implode(', ', $validator->errors()->all());
            return;
        }

        $name = $row['name'];
        $email = $row['email'] ?? '';
        $phone = $row['phone'] ?? '';
        $type = strtolower($row['type'] ?? 'lead');

        // Check for duplicates if enabled
        if ($this->skipDuplicates && $email) {
            if ($type === 'lead' || $type === 'Lead') {
                $exists = Lead::where('email', $email)
                    ->where('created_by', Auth::user()->creatorId())
                    ->exists();
            } else {
                // For deals, check by name since they don't have email
                $exists = Deal::where('name', $name)
                    ->where('created_by', Auth::user()->creatorId())
                    ->exists();
            }

            if ($exists) {
                $this->results['skipped']++;
                return;
            }
        }

        try {
            if ($type === 'lead' || $type === 'Lead') {
                $this->createLead($row);
            } else {
                $this->createDeal($row);
            }
            $this->results['imported']++;
        } catch (\Exception $e) {
            $this->results['errors']++;
            $this->results['error_messages'][] = "Error creating {$type} '{$name}': " . $e->getMessage();
        }
    }

    protected function createLead($row)
    {
        $pipelineId = $this->getPipelineId($row['pipeline'] ?? null);
        $stageId = $this->getStageId($row['stage'] ?? null, $pipelineId);

        Lead::create([
            'name' => $row['name'],
            'email' => $row['email'] ?? '',
            'phone' => $row['phone'] ?? '',
            'subject' => $row['subject'] ?? 'Imported Lead',
            'pipeline_id' => $pipelineId,
            'stage_id' => $stageId,
            'user_id' => Auth::user()->id,
            'created_by' => Auth::user()->creatorId(),
        ]);
    }

    protected function createDeal($row)
    {
        $pipelineId = $this->getPipelineId($row['pipeline'] ?? null);
        $stageId = $this->getStageId($row['stage'] ?? null, $pipelineId);

        Deal::create([
            'name' => $row['name'],
            'phone' => $row['phone'] ?? '',
            'price' => $row['price'] ?? 0,
            'pipeline_id' => $pipelineId,
            'stage_id' => $stageId,
            'created_by' => Auth::user()->creatorId(),
        ]);
    }

    protected function getPipelineId($pipelineName)
    {
        if ($pipelineName) {
            $pipeline = Pipeline::where('name', $pipelineName)
                ->where('created_by', Auth::user()->creatorId())
                ->first();
            if ($pipeline) {
                return $pipeline->id;
            }
        }

        return $this->defaultPipelineId ?: Pipeline::where('created_by', Auth::user()->creatorId())->first()?->id;
    }

    protected function getStageId($stageName, $pipelineId)
    {
        if ($stageName && $pipelineId) {
            $stage = Stage::where('name', $stageName)
                ->where('pipeline_id', $pipelineId)
                ->where('created_by', Auth::user()->creatorId())
                ->first();
            if ($stage) {
                return $stage->id;
            }
        }

        return $this->defaultStageId ?: Stage::where('pipeline_id', $pipelineId)
            ->where('created_by', Auth::user()->creatorId())
            ->orderBy('order')
            ->first()?->id;
    }

    public function getResults()
    {
        return $this->results;
    }
}
