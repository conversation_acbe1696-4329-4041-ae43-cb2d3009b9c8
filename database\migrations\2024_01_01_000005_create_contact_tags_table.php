<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_tags', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('contact_id');
            $table->enum('contact_type', ['lead', 'deal']);
            $table->unsignedBigInteger('label_id');
            $table->unsignedBigInteger('added_by')->nullable();
            $table->timestamps();

            $table->foreign('label_id')->references('id')->on('labels')->onDelete('cascade');
            $table->foreign('added_by')->references('id')->on('users')->onDelete('set null');
            
            // Ensure unique tag per contact
            $table->unique(['contact_id', 'contact_type', 'label_id']);
            $table->index(['contact_id', 'contact_type']);
            $table->index(['label_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_tags');
    }
};
