<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ContactGroup extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'created_by',
    ];

    /**
     * Get the user who created this contact group
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get all contacts in this group
     */
    public function contacts()
    {
        return $this->hasMany(ContactGroupMember::class, 'group_id');
    }

    /**
     * Get leads in this group
     */
    public function leads()
    {
        return $this->hasManyThrough(
            Lead::class,
            ContactGroupMember::class,
            'group_id',
            'id',
            'id',
            'contact_id'
        )->where('contact_type', 'lead');
    }

    /**
     * Get deals in this group
     */
    public function deals()
    {
        return $this->hasManyThrough(
            Deal::class,
            ContactGroupMember::class,
            'group_id',
            'id',
            'id',
            'contact_id'
        )->where('contact_type', 'deal');
    }

    /**
     * Add a contact to this group
     */
    public function addContact($contactId, $contactType)
    {
        return ContactGroupMember::firstOrCreate([
            'group_id' => $this->id,
            'contact_id' => $contactId,
            'contact_type' => $contactType,
        ]);
    }

    /**
     * Remove a contact from this group
     */
    public function removeContact($contactId, $contactType)
    {
        return ContactGroupMember::where([
            'group_id' => $this->id,
            'contact_id' => $contactId,
            'contact_type' => $contactType,
        ])->delete();
    }

    /**
     * Check if a contact is in this group
     */
    public function hasContact($contactId, $contactType)
    {
        return ContactGroupMember::where([
            'group_id' => $this->id,
            'contact_id' => $contactId,
            'contact_type' => $contactType,
        ])->exists();
    }

    /**
     * Get contact count for this group
     */
    public function getContactCountAttribute()
    {
        return $this->contacts()->count();
    }
}
