<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_group_members', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('group_id');
            $table->unsignedBigInteger('contact_id');
            $table->enum('contact_type', ['lead', 'deal']);
            $table->unsignedBigInteger('added_by')->nullable();
            $table->timestamps();

            $table->foreign('group_id')->references('id')->on('contact_groups')->onDelete('cascade');
            $table->foreign('added_by')->references('id')->on('users')->onDelete('set null');
            
            // Ensure unique contact per group
            $table->unique(['group_id', 'contact_id', 'contact_type']);
            $table->index(['contact_id', 'contact_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_group_members');
    }
};
