<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ContactWorkflowAssignment extends Model
{
    use HasFactory;

    protected $fillable = [
        'workflow_id',
        'contact_id',
        'contact_type',
        'notes',
        'status',
        'assigned_by',
        'completed_at',
    ];

    protected $casts = [
        'completed_at' => 'datetime',
    ];

    const STATUS_PENDING = 'pending';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';

    public static $statuses = [
        self::STATUS_PENDING => 'Pending',
        self::STATUS_IN_PROGRESS => 'In Progress',
        self::STATUS_COMPLETED => 'Completed',
        self::STATUS_CANCELLED => 'Cancelled',
    ];

    /**
     * Get the workflow
     */
    public function workflow()
    {
        return $this->belongsTo(ContactWorkflow::class, 'workflow_id');
    }

    /**
     * Get the user who assigned this workflow
     */
    public function assignedBy()
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Get the contact (polymorphic relationship)
     */
    public function contact()
    {
        if ($this->contact_type === 'lead') {
            return $this->belongsTo(Lead::class, 'contact_id');
        } elseif ($this->contact_type === 'deal') {
            return $this->belongsTo(Deal::class, 'contact_id');
        }
        
        return null;
    }

    /**
     * Get the lead if contact type is lead
     */
    public function lead()
    {
        return $this->belongsTo(Lead::class, 'contact_id');
    }

    /**
     * Get the deal if contact type is deal
     */
    public function deal()
    {
        return $this->belongsTo(Deal::class, 'contact_id');
    }

    /**
     * Scope to filter by contact type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('contact_type', $type);
    }

    /**
     * Scope to filter by status
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Mark as completed
     */
    public function markCompleted()
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'completed_at' => now(),
        ]);
    }

    /**
     * Mark as in progress
     */
    public function markInProgress()
    {
        $this->update([
            'status' => self::STATUS_IN_PROGRESS,
        ]);
    }

    /**
     * Mark as cancelled
     */
    public function markCancelled()
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
        ]);
    }

    /**
     * Get contact name based on type
     */
    public function getContactNameAttribute()
    {
        if ($this->contact_type === 'lead' && $this->lead) {
            return $this->lead->name;
        } elseif ($this->contact_type === 'deal' && $this->deal) {
            return $this->deal->name;
        }
        
        return 'Unknown Contact';
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute()
    {
        return self::$statuses[$this->status] ?? 'Unknown';
    }
}
