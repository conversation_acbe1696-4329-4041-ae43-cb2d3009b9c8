{{ Form::open(array('url' => 'leads', 'class'=>'needs-validation', 'novalidate')) }}
<div class="modal-body" style="max-height: 80vh; overflow-y: auto;">
    {{-- start for ai module--}}
    @php
        $plan= \App\Models\Utility::getChatGPTSettings();
    @endphp
    @if($plan->chatgpt == 1)
    <div class="text-end">
        <a href="#" data-size="md" class="btn  btn-primary btn-icon btn-sm" data-ajax-popup-over="true" data-url="{{ route('generate',['lead']) }}"
           data-bs-placement="top" data-title="{{ __('Generate content with AI') }}">
            <i class="fas fa-robot"></i> <span>{{__('Generate with AI')}}</span>
        </a>
    </div>
    @endif
    {{-- end for ai module--}}
    
    <!-- Basic Information Section -->
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3">{{ __('Basic Information') }}</h6>
        </div>  
        <div class="col-6 form-group">
            {{ Form::label('name', __('Name'),['class'=>'form-label']) }}<x-required></x-required>
            {{ Form::text('name', null, array('class' => 'form-control','required'=>'required' , 'placeholder' => __('Enter Name'))) }}
        </div>
        <div class="col-6 form-group">
            {{ Form::label('email', __('Email'),['class'=>'form-label']) }}<x-required></x-required>
            {{ Form::text('email', null, array('class' => 'form-control','required'=>'required' , 'placeholder' => __('Enter email'))) }}
        </div>
        <div class="col-6 form-group">
            <x-mobile label="{{__('Phone')}}" name="phone" value="{{old('phone')}}" required placeholder="Enter Phone"></x-mobile>
        </div>
        <div class="col-6 form-group">
            {{ Form::label('user_id', __('User'),['class'=>'form-label']) }}<x-required></x-required>
            {{ Form::select('user_id', $users,null, array('class' => 'form-control select','required'=>'required')) }}
            <div class="text-xs mt-1">
                {{ __('Create user here.') }} <a href="{{ route('users.index') }}"><b>{{ __('Create user') }}</b></a>
            </div>
        </div>
        <div class="col-6 form-group">
            {{ Form::label('date_of_birth', __('Date of Birth'),['class'=>'form-label']) }}
            {{ Form::date('date_of_birth', old('date_of_birth'), ['class' => 'form-control', 'placeholder' => __('Select date')]) }}
        </div>
        <div class="col-6 form-group">
            {{ Form::label('next_follow_up_date', __('Next Follow-Up Date'),['class'=>'form-label']) }}
            {{ Form::date('next_follow_up_date', old('next_follow_up_date'), ['class' => 'form-control', 'placeholder' => __('Select date')]) }}
        </div>
        <div class="col-6 form-group">
            {{ Form::label('type', __('Type'),['class'=>'form-label']) }}
            {{ Form::select('type', [
                'lead' => __('Lead'),
                'customer' => __('Customer'),
            ], old('type'), ['class' => 'form-control select', 'placeholder' => __('Select Type')]) }}
        </div>
    </div>

    <!-- Pipeline & Stage Section -->
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3">{{ __('Pipeline & Stage') }}</h6>
        </div>
        <div class="col-6 form-group">
            {{ Form::label('pipeline_id', __('Pipeline'),['class'=>'form-label']) }}
            {{ Form::select('pipeline_id', $pipelines ?? [], old('pipeline_id'), ['class' => 'form-control select', 'placeholder' => __('Select Pipeline')]) }}
        </div>
        <div class="col-6 form-group">
            {{ Form::label('stage_id', __('Stage'),['class'=>'form-label']) }}
            {{ Form::select('stage_id', [], old('stage_id'), ['class' => 'form-control select', 'placeholder' => __('Select Stage'), 'id' => 'stage-select']) }}
            <small class="text-muted" id="stage-help-text">{{ __('Select a pipeline first to load stages') }}</small>
        </div>
        <div class="col-6 form-group">
            {{ Form::label('status', __('Status'),['class'=>'form-label']) }}
            {{ Form::select('status', [
                'cold' => __('Cold Lead'),
                'warm' => __('Warm Lead'),
                'hot' => __('Hot Lead'),
                'won' => __('Won Lead'),
                'lost' => __('Lost Lead')       
            ], old('status'), ['class' => 'form-control select', 'placeholder' => __('Select Status')]) }}
        </div>
    </div>

    <!-- Opportunity Information Section -->
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3">{{ __('Opportunity Information') }}</h6>
        </div>
        <div class="col-6 form-group">
            {{ Form::label('opportunity_info', __('Opportunity Info'),['class'=>'form-label']) }}
            {{ Form::text('opportunity_info', old('opportunity_info'), ['class' => 'form-control', 'placeholder' => __('Enter Opportunity Info')]) }}
        </div>
        <div class="col-6 form-group">
            {{ Form::label('opportunity_source', __('Opportunity Source'),['class'=>'form-label']) }}
            {{ Form::select('opportunity_source', [
                'website' => __('Website'),
                'referral' => __('Referral'),
                'social_media' => __('Social Media'),
                'email' => __('Email'),
                'phone' => __('Phone'),
                'advertisement' => __('Advertisement'),
                'other' => __('Other')
            ], old('opportunity_source'), ['class' => 'form-control select', 'placeholder' => __('Select Source')]) }}
        </div>
        <div class="col-6 form-group">
            {{ Form::label('lead_value', __('Lead Value'),['class'=>'form-label']) }}
            {{ Form::number('lead_value', old('lead_value'), ['class' => 'form-control', 'placeholder' => __('Enter Value'), 'step' => '0.01', 'min' => '0']) }}
        </div>
        <div class="col-12 form-group">
            {{ Form::label('opportunity_description', __('Opportunity Description'),['class'=>'form-label']) }}
            {{ Form::textarea('opportunity_description', old('opportunity_description'), ['class' => 'form-control', 'rows' => '3', 'placeholder' => __('Enter Opportunity Description')]) }}
        </div>
    </div>

    <!-- Tags/Labels Section -->
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3">
                <i class="fas fa-tags me-2"></i>{{ __('Tags & Labels') }}
            </h6>
        </div>
        <div class="col-12 form-group">
            {{ Form::label('labels', __('Tags/Labels'),['class'=>'form-label']) }}
            {{ Form::select('labels[]', $labels ?? [], old('labels'), ['class' => 'form-control select', 'multiple' => 'multiple', 'placeholder' => __('Select Tags/Labels'), 'id' => 'labels-select']) }}
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                {{ __('You can select existing tags or type to create new ones. New tags will be created automatically.') }}
            </small>
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="{{__('Cancel')}}" class="btn  btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{__('Create')}}" class="btn  btn-primary">
</div>

{{Form::close()}}

<style>
.select2-container--default .select2-results__option[aria-selected] {
    background-color: #e3f2fd;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #2196f3;
    color: white;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #2196f3;
    border: 1px solid #2196f3;
    color: white;
    border-radius: 4px;
    padding: 2px 8px;
    margin: 2px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: white;
    margin-right: 5px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #f0f0f0;
}

/* Stage loading styles */
#stage-select:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

#stage-help-text {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

#stage-help-text i {
    font-size: 0.75rem;
}
</style>

<script>
$(document).ready(function() {
    // Initialize Select2 for labels with tag creation functionality
    $('#labels-select').select2({
        placeholder: '{{ __("Select Tags/Labels") }}',
        allowClear: true,
        tags: true, // Enable tag creation
        createTag: function(params) {
            // Check if the term is empty
            if (params.term.trim() === '') {
                return undefined;
            }
            
            // Check if the term already exists
            var existingOptions = $('#labels-select option');
            for (var i = 0; i < existingOptions.length; i++) {
                if (existingOptions[i].text.toLowerCase() === params.term.toLowerCase()) {
                    return undefined;
                }
            }
            
            return {
                id: 'new_' + params.term, // Prefix with 'new_' to identify new tags
                text: params.term,
                newTag: true
            };
        },
        templateResult: function(data) {
            if (data.newTag) {
                return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + ' <small class="text-muted">(new)</small></span>');
            }
            return data.text;
        }
    }).on('select2:select', function(e) {
        // Show notification when a new tag is created
        if (e.params.data && e.params.data.newTag) {
            if (typeof show_toastr !== 'undefined') {
                show_toastr('success', 'New tag "' + e.params.data.text + '" will be created when you save the lead.', 'success');
            }
        }
    });
    
    // Handle pipeline change to load stages
    $('select[name="pipeline_id"]').on('change', function() {
        var pipelineId = $(this).val();
        var stageSelect = $('select[name="stage_id"]');
        var stageHelpText = $('#stage-help-text');
        
        if (pipelineId) {
            // Clear current stages
            stageSelect.empty().append('<option value="">{{ __("Select Stage") }}</option>');
            
            // Show loading state
            stageSelect.prop('disabled', true);
            stageHelpText.html('<i class="fas fa-spinner fa-spin me-1"></i>{{ __("Loading stages...") }}');
            
            // Load stages for selected pipeline using direct database query
            var url = '{{ route("leads.pipelineStages") }}';
            console.log('Calling URL:', url, 'with pipeline_id:', pipelineId);
            
            $.ajax({
                url: url,
                type: 'GET',
                data: {
                    pipeline_id: pipelineId
                },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    console.log('Stages loaded:', response);
                    
                    if (response.success && response.stages && response.stages.length > 0) {
                        response.stages.forEach(function(stage) {
                            stageSelect.append('<option value="' + stage.id + '">' + stage.name + '</option>');
                        });
                        
                        stageHelpText.html('<i class="fas fa-check-circle text-success me-1"></i>' + response.stages.length + ' {{ __("stages available") }}');
                        
                        // Show success message
                        if (typeof show_toastr !== 'undefined') {
                            show_toastr('success', response.stages.length + ' stages loaded for this pipeline.', 'success');
                        }
                    } else {
                        stageSelect.append('<option value="" disabled>{{ __("No stages found for this pipeline") }}</option>');
                        stageHelpText.html('<i class="fas fa-exclamation-triangle text-warning me-1"></i>{{ __("No stages found for this pipeline") }}');
                        
                        if (typeof show_toastr !== 'undefined') {
                            show_toastr('warning', 'No stages found for this pipeline.', 'warning');
                        }
                    }
                },
                error: function(xhr) {
                    console.error('Error loading stages:', xhr);
                    console.error('Status:', xhr.status);
                    console.error('Response:', xhr.responseText);
                    
                    var errorMessage = '{{ __("Error loading stages") }}';
                    if (xhr.status === 403) {
                        errorMessage = '{{ __("Permission denied") }}';
                    } else if (xhr.status === 404) {
                        errorMessage = '{{ __("Pipeline not found") }}';
                    } else if (xhr.status === 500) {
                        errorMessage = '{{ __("Server error") }}';
                    }
                    
                    stageSelect.append('<option value="" disabled>' + errorMessage + '</option>');
                    stageHelpText.html('<i class="fas fa-times-circle text-danger me-1"></i>' + errorMessage);
                    
                    if (typeof show_toastr !== 'undefined') {
                        show_toastr('error', errorMessage, 'error');
                    }
                },
                complete: function() {
                    // Re-enable the select
                    stageSelect.prop('disabled', false);
                }
            });
        } else {
            stageSelect.empty().append('<option value="">{{ __("Select Stage") }}</option>');
            stageHelpText.html('{{ __("Select a pipeline first to load stages") }}');
        }
    });
});
</script>

