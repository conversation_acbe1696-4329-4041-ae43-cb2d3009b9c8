@extends('layouts.admin')

@section('page-title')
    {{ __('Contacts') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Contacts') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-8">
                            <h5>{{ __('Contacts') }}</h5>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-end">
                                @php
                                    $hasFilters = request('filter_name') || request('filter_email') || request('filter_phone') || request('filter_type');
                                @endphp

                                <!-- Action Buttons Section -->
                                <div class="d-inline-flex align-items-center">
                                    <!-- Toggle Button for Actions -->
                                    <button class="btn btn-sm btn-secondary me-2" type="button" id="toggleActionsBtn" data-bs-toggle="tooltip" title="{{ __('Toggle Actions') }}">
                                        <i class="fa fa-ellipsis-h"></i>
                                    </button>

                                    <!-- Action Buttons Container (Initially Hidden) -->
                                    <div id="actionButtonsContainer" class="d-none">
                                        <div class="btn-group me-2" role="group">
                                            <!-- Add New Contact -->
                                            <button class="btn btn-sm btn-success" type="button" data-bs-toggle="modal" data-bs-target="#addContactModal" data-bs-toggle="tooltip" title="{{ __('Add a New Contact') }}">
                                                <i class="fa fa-user-plus"></i>
                                            </button>

                                            <!-- Add to Contact Group -->
                                            <button class="btn btn-sm btn-info" type="button" data-bs-toggle="modal" data-bs-target="#addToGroupModal" data-bs-toggle="tooltip" title="{{ __('Add to a Contact Group') }}">
                                                <i class="fa fa-users"></i>
                                            </button>

                                            <!-- Add to Workflow -->
                                            <button class="btn btn-sm btn-warning" type="button" data-bs-toggle="modal" data-bs-target="#addToWorkflowModal" data-bs-toggle="tooltip" title="{{ __('Add to Workflow') }}">
                                                <i class="fa fa-project-diagram"></i>
                                            </button>

                                            <!-- Add Tag -->
                                            <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="modal" data-bs-target="#addTagModal" data-bs-toggle="tooltip" title="{{ __('Add Tag') }}">
                                                <i class="fa fa-tag"></i>
                                            </button>

                                            <!-- Appointment Booking -->
                                            <button class="btn btn-sm btn-purple" type="button" data-bs-toggle="modal" data-bs-target="#appointmentBookingModal" data-bs-toggle="tooltip" title="{{ __('Appointment Booking') }}">
                                                <i class="fa fa-calendar-plus"></i>
                                            </button>

                                            <!-- Bulk Import -->
                                            <button class="btn btn-sm btn-dark" type="button" data-bs-toggle="modal" data-bs-target="#bulkImportModal" data-bs-toggle="tooltip" title="{{ __('Bulk Import') }}">
                                                <i class="fa fa-upload"></i>
                                            </button>

                                            <!-- Bulk Export -->
                                            <button class="btn btn-sm btn-secondary" type="button" onclick="exportContacts()" data-bs-toggle="tooltip" title="{{ __('Bulk Export') }}">
                                                <i class="fa fa-download"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Search Filter Button -->
                                    <button class="btn btn-sm {{ $hasFilters ? 'btn-warning' : 'btn-primary' }}" type="button" data-bs-toggle="offcanvas" data-bs-target="#contactsFilter" aria-controls="contactsFilter" data-bs-toggle="tooltip" title="{{ __('Search Filter') }}">
                                        <i class="fa fa-filter"></i>
                                        @if($hasFilters)
                                            <span class="badge bg-light text-dark ms-1">{{ __('Active') }}</span>
                                        @endif
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if($hasFilters)
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <strong>{{ __('Active Filters:') }}</strong>
                            @if(request('filter_name'))
                                <span class="badge bg-primary me-1">{{ __('Name') }}: {{ request('filter_name') }}</span>
                            @endif
                            @if(request('filter_email'))
                                <span class="badge bg-primary me-1">{{ __('Email') }}: {{ request('filter_email') }}</span>
                            @endif
                            @if(request('filter_phone'))
                                <span class="badge bg-primary me-1">{{ __('Phone') }}: {{ request('filter_phone') }}</span>
                            @endif
                            @if(request('filter_type'))
                                <span class="badge bg-primary me-1">{{ __('Type') }}:
                                    @if(request('filter_type') == 'WhatsApp')
                                        {{ __('External Contact') }}
                                    @else
                                        {{ request('filter_type') }}
                                    @endif
                                </span>
                            @endif
                            <a href="{{ route('contacts.index') }}" class="btn btn-sm btn-outline-secondary ms-2">
                                <i class="fa fa-times"></i> {{ __('Clear All') }}
                            </a>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-striped" id="contacts-table">
                            <thead>
                                <tr>
                                    <th class="text-center">{{ __('Name, Email & Phone') }}</th>
                                    <th class="text-center">{{ __('Type') }}</th>
                                    <th class="text-center">{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Loading indicator for external contacts -->
                                <tr id="loading-external" style="display: none;">
                                    <td colspan="3" class="text-center py-4">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </td>
                                </tr>
                                @forelse($contacts as $contact)
                                    <tr>
                                        <td class="text-center">
                                            <div>
                                                <strong>{{ $contact['name'] }}</strong><br>
                                                @if($contact['email'])
                                                    <small class="text-muted">
                                                        <i class="fa fa-envelope"></i> {{ $contact['email'] }}
                                                    </small><br>
                                                @endif
                                                @if($contact['phone'])
                                                    <small class="text-muted">
                                                        <i class="fa fa-phone"></i> {{ $contact['phone'] }}
                                                    </small>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            @php
                                                $badgeClass = 'info';
                                                $displayType = $contact['type'];
                                                if ($contact['type'] == 'Lead') {
                                                    $badgeClass = 'success';
                                                } elseif ($contact['type'] == 'Deal') {
                                                    $badgeClass = 'info';
                                                } elseif ($contact['type'] == 'WhatsApp') {
                                                    $badgeClass = 'warning';
                                                    $displayType = 'External Contact';
                                                }
                                            @endphp
                                            <span class="badge bg-{{ $badgeClass }}">
                                                <i class="fab fa-whatsapp"></i> {{ $displayType }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            @if($contact['type'] == 'Lead')
                                                <a href="{{ route('leads.show', $contact['id']) }}" class="btn btn-sm btn-primary me-2">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-warning me-2" onclick="editContact('{{ $contact['id'] }}', 'lead')">
                                                    <i class="fa fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteContact('{{ $contact['id'] }}', 'lead', '{{ $contact['name'] }}')">
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            @elseif($contact['type'] == 'Deal')
                                                <a href="{{ route('deals.show', $contact['id']) }}" class="btn btn-sm btn-primary me-2">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-warning me-2" onclick="editContact('{{ $contact['id'] }}', 'deal')">
                                                    <i class="fa fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteContact('{{ $contact['id'] }}', 'deal', '{{ $contact['name'] }}')">
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            @elseif($contact['type'] == 'WhatsApp')
                                                <span class="badge bg-secondary">
                                                    <i class="fab fa-whatsapp"></i> {{ __('External Contact') }}
                                                </span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="3" class="text-center py-4">
                                            <div class="text-center">
                                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                                <h5 class="text-muted">{{ __('No contacts found') }}</h5>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Off-canvas -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="contactsFilter" aria-labelledby="contactsFilterLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="contactsFilterLabel">{{ __('Filter Contacts') }}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <form method="GET" action="{{ route('contacts.index') }}" id="contactsFilterForm">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_name">{{ __('Name') }}</label>
                            <input type="text" class="form-control" name="filter_name" id="filter_name"
                                   value="{{ request('filter_name') }}" placeholder="{{ __('Enter name...') }}">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_email">{{ __('Email') }}</label>
                            <input type="email" class="form-control" name="filter_email" id="filter_email"
                                   value="{{ request('filter_email') }}" placeholder="{{ __('Enter email...') }}">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_phone">{{ __('Phone') }}</label>
                            <input type="text" class="form-control" name="filter_phone" id="filter_phone"
                                   value="{{ request('filter_phone') }}" placeholder="{{ __('Enter phone...') }}">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_type">{{ __('Type') }}</label>
                            <select class="form-control" name="filter_type" id="filter_type">
                                <option value="">{{ __('All Types') }}</option>
                                <option value="Lead" {{ request('filter_type') == 'Lead' ? 'selected' : '' }}>{{ __('Lead') }}</option>
                                <option value="Deal" {{ request('filter_type') == 'Deal' ? 'selected' : '' }}>{{ __('Deal') }}</option>
                                <option value="WhatsApp" {{ request('filter_type') == 'WhatsApp' ? 'selected' : '' }}>{{ __('External Contact') }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_pipeline">{{ __('Pipeline') }}</label>
                            <select class="form-control" name="filter_pipeline" id="filter_pipeline">
                                <option value="">{{ __('All Pipelines') }}</option>
                                @foreach($pipelines as $pipeline)
                                    <option value="{{ $pipeline->id }}" {{ request('filter_pipeline') == $pipeline->id ? 'selected' : '' }}>
                                        {{ $pipeline->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_stage">{{ __('Stage') }}</label>
                            <select class="form-control" name="filter_stage" id="filter_stage">
                                <option value="">{{ __('All Stages') }}</option>
                                <!-- Stages will be loaded dynamically based on pipeline selection -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_date_from">{{ __('Created From') }}</label>
                            <input type="date" class="form-control" name="filter_date_from" id="filter_date_from"
                                   value="{{ request('filter_date_from') }}">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_date_to">{{ __('Created To') }}</label>
                            <input type="date" class="form-control" name="filter_date_to" id="filter_date_to"
                                   value="{{ request('filter_date_to') }}">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_tags">{{ __('Tags') }}</label>
                            <select class="form-control" name="filter_tags[]" id="filter_tags" multiple>
                                <!-- Tags will be loaded dynamically -->
                            </select>
                            <small class="form-text text-muted">{{ __('Select multiple tags to filter by') }}</small>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_group">{{ __('Contact Group') }}</label>
                            <select class="form-control" name="filter_group" id="filter_group">
                                <option value="">{{ __('All Groups') }}</option>
                                <!-- Groups will be loaded dynamically -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_workflow">{{ __('Workflow') }}</label>
                            <select class="form-control" name="filter_workflow" id="filter_workflow">
                                <option value="">{{ __('All Workflows') }}</option>
                                <!-- Workflows will be loaded dynamically -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_sort">{{ __('Sort By') }}</label>
                            <select class="form-control" name="filter_sort" id="filter_sort">
                                <option value="name_asc" {{ request('filter_sort') == 'name_asc' ? 'selected' : '' }}>{{ __('Name (A-Z)') }}</option>
                                <option value="name_desc" {{ request('filter_sort') == 'name_desc' ? 'selected' : '' }}>{{ __('Name (Z-A)') }}</option>
                                <option value="created_desc" {{ request('filter_sort') == 'created_desc' ? 'selected' : '' }}>{{ __('Newest First') }}</option>
                                <option value="created_asc" {{ request('filter_sort') == 'created_asc' ? 'selected' : '' }}>{{ __('Oldest First') }}</option>
                                <option value="email_asc" {{ request('filter_sort') == 'email_asc' ? 'selected' : '' }}>{{ __('Email (A-Z)') }}</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <button type="submit" class="btn btn-primary w-100">
                            {{ __('Apply Filter') }}
                        </button>
                    </div>
                    <div class="col-md-6">
                        <a href="{{ route('contacts.index') }}" class="btn btn-secondary w-100">
                            {{ __('Clear Filter') }}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Contact Modal -->
    <div class="modal fade" id="editContactModal" tabindex="-1" aria-labelledby="editContactModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editContactModalLabel">{{ __('Edit Contact') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="editContactForm">
                        <!-- Form will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Contact Modal -->
    <div class="modal fade" id="deleteContactModal" tabindex="-1" aria-labelledby="deleteContactModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteContactModalLabel">{{ __('Delete Contact') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5>{{ __('Are you sure?') }}</h5>
                        <p class="text-muted" id="deleteContactMessage">
                            {{ __('This action cannot be undone.') }}
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">{{ __('Delete') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Convert to Lead Modal -->
    <div class="modal fade" id="convertToLeadModal" tabindex="-1" aria-labelledby="convertToLeadModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="convertToLeadModalLabel">{{ __('Convert to Lead') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="convertToLeadForm">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="contact_name" class="form-label">{{ __('Contact Name') }}</label>
                                    <input type="text" class="form-control" id="contact_name" name="contact_name" readonly>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="contact_email" class="form-label">{{ __('Contact Email') }}</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" readonly>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="contact_phone" class="form-label">{{ __('Contact Phone') }}</label>
                                    <input type="text" class="form-control" id="contact_phone" name="contact_phone" readonly>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="pipeline_id" class="form-label">{{ __('Pipeline') }} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="pipeline_id" name="pipeline_id" required>
                                        <option value="">{{ __('Select Pipeline') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="stage_id" class="form-label">{{ __('Stage') }} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="stage_id" name="stage_id" required>
                                        <option value="">{{ __('Select Stage') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="external_id" name="external_id">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-primary" id="confirmConvertBtn">{{ __('Convert to Lead') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add New Contact Modal -->
    <div class="modal fade" id="addContactModal" tabindex="-1" aria-labelledby="addContactModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addContactModalLabel">{{ __('Add New Contact') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addContactForm" action="{{ route('contacts.store') }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="contact_type" class="form-label">{{ __('Contact Type') }} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="contact_type" name="contact_type" required>
                                        <option value="">{{ __('Select Type') }}</option>
                                        <option value="lead">{{ __('Lead') }}</option>
                                        <option value="deal">{{ __('Deal') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="new_contact_name" class="form-label">{{ __('Name') }} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="new_contact_name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="new_contact_email" class="form-label">{{ __('Email') }}</label>
                                    <input type="email" class="form-control" id="new_contact_email" name="email">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="new_contact_phone" class="form-label">{{ __('Phone') }}</label>
                                    <input type="text" class="form-control" id="new_contact_phone" name="phone">
                                </div>
                            </div>
                            <div class="col-md-12" id="leadFields" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="new_pipeline_id" class="form-label">{{ __('Pipeline') }} <span class="text-danger">*</span></label>
                                            <select class="form-control" id="new_pipeline_id" name="pipeline_id">
                                                <option value="">{{ __('Select Pipeline') }}</option>
                                                @foreach($pipelines as $pipeline)
                                                    <option value="{{ $pipeline->id }}">{{ $pipeline->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="new_stage_id" class="form-label">{{ __('Stage') }} <span class="text-danger">*</span></label>
                                            <select class="form-control" id="new_stage_id" name="stage_id">
                                                <option value="">{{ __('Select Stage') }}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group mb-3">
                                            <label for="new_contact_subject" class="form-label">{{ __('Subject') }}</label>
                                            <input type="text" class="form-control" id="new_contact_subject" name="subject">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12" id="dealFields" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="new_deal_pipeline_id" class="form-label">{{ __('Pipeline') }} <span class="text-danger">*</span></label>
                                            <select class="form-control" id="new_deal_pipeline_id" name="deal_pipeline_id">
                                                <option value="">{{ __('Select Pipeline') }}</option>
                                                @foreach($pipelines as $pipeline)
                                                    <option value="{{ $pipeline->id }}">{{ $pipeline->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="new_deal_stage_id" class="form-label">{{ __('Stage') }} <span class="text-danger">*</span></label>
                                            <select class="form-control" id="new_deal_stage_id" name="deal_stage_id">
                                                <option value="">{{ __('Select Stage') }}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group mb-3">
                                            <label for="new_deal_price" class="form-label">{{ __('Price') }}</label>
                                            <input type="number" class="form-control" id="new_deal_price" name="price" step="0.01">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-success" onclick="submitAddContactForm()">{{ __('Add Contact') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add to Contact Group Modal -->
    <div class="modal fade" id="addToGroupModal" tabindex="-1" aria-labelledby="addToGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addToGroupModalLabel">{{ __('Add to Contact Group') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addToGroupForm">
                        @csrf
                        <div class="form-group mb-3">
                            <label for="selected_contacts" class="form-label">{{ __('Selected Contacts') }}</label>
                            <div id="selectedContactsList" class="border rounded p-2 mb-3" style="min-height: 60px; background-color: #f8f9fa;">
                                <small class="text-muted">{{ __('Please select contacts from the table first') }}</small>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <label for="contact_group" class="form-label">{{ __('Contact Group') }} <span class="text-danger">*</span></label>
                            <select class="form-control" id="contact_group" name="group_id" required>
                                <option value="">{{ __('Select Group') }}</option>
                                <!-- Groups will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label for="new_group_name" class="form-label">{{ __('Or Create New Group') }}</label>
                            <input type="text" class="form-control" id="new_group_name" name="new_group_name" placeholder="{{ __('Enter new group name') }}">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-info" onclick="submitAddToGroupForm()">{{ __('Add to Group') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add to Workflow Modal -->
    <div class="modal fade" id="addToWorkflowModal" tabindex="-1" aria-labelledby="addToWorkflowModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addToWorkflowModalLabel">{{ __('Add to Workflow') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addToWorkflowForm">
                        @csrf
                        <div class="form-group mb-3">
                            <label for="workflow_selected_contacts" class="form-label">{{ __('Selected Contacts') }}</label>
                            <div id="workflowSelectedContactsList" class="border rounded p-2 mb-3" style="min-height: 60px; background-color: #f8f9fa;">
                                <small class="text-muted">{{ __('Please select contacts from the table first') }}</small>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <label for="workflow_id" class="form-label">{{ __('Workflow') }} <span class="text-danger">*</span></label>
                            <select class="form-control" id="workflow_id" name="workflow_id" required>
                                <option value="">{{ __('Select Workflow') }}</option>
                                <!-- Workflows will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label for="workflow_notes" class="form-label">{{ __('Notes') }}</label>
                            <textarea class="form-control" id="workflow_notes" name="notes" rows="3" placeholder="{{ __('Optional notes for workflow assignment') }}"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-warning" onclick="submitAddToWorkflowForm()">{{ __('Add to Workflow') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Tag Modal -->
    <div class="modal fade" id="addTagModal" tabindex="-1" aria-labelledby="addTagModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addTagModalLabel">{{ __('Add Tag') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addTagForm">
                        @csrf
                        <div class="form-group mb-3">
                            <label for="tag_selected_contacts" class="form-label">{{ __('Selected Contacts') }}</label>
                            <div id="tagSelectedContactsList" class="border rounded p-2 mb-3" style="min-height: 60px; background-color: #f8f9fa;">
                                <small class="text-muted">{{ __('Please select contacts from the table first') }}</small>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <label for="contact_tags" class="form-label">{{ __('Tags') }} <span class="text-danger">*</span></label>
                            <select class="form-control" id="contact_tags" name="tags[]" multiple required>
                                <!-- Tags will be loaded dynamically -->
                            </select>
                            <small class="form-text text-muted">{{ __('You can select multiple tags or create new ones by typing') }}</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-primary" onclick="submitAddTagForm()">{{ __('Add Tags') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Appointment Booking Modal -->
    <div class="modal fade" id="appointmentBookingModal" tabindex="-1" aria-labelledby="appointmentBookingModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="appointmentBookingModalLabel">{{ __('Appointment Booking') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="appointmentBookingForm">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="appointment_contact" class="form-label">{{ __('Contact') }} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="appointment_contact" name="contact_id" required>
                                        <option value="">{{ __('Select Contact') }}</option>
                                        <!-- Contacts will be loaded dynamically -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="appointment_date" class="form-label">{{ __('Date') }} <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="appointment_date" name="appointment_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="appointment_time" class="form-label">{{ __('Time') }} <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control" id="appointment_time" name="appointment_time" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="appointment_duration" class="form-label">{{ __('Duration (minutes)') }} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="appointment_duration" name="duration" required>
                                        <option value="15">15 {{ __('minutes') }}</option>
                                        <option value="30" selected>30 {{ __('minutes') }}</option>
                                        <option value="45">45 {{ __('minutes') }}</option>
                                        <option value="60">1 {{ __('hour') }}</option>
                                        <option value="90">1.5 {{ __('hours') }}</option>
                                        <option value="120">2 {{ __('hours') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="appointment_title" class="form-label">{{ __('Title') }} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="appointment_title" name="title" required placeholder="{{ __('Enter appointment title') }}">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="appointment_location" class="form-label">{{ __('Location') }}</label>
                                    <input type="text" class="form-control" id="appointment_location" name="location" placeholder="{{ __('Enter location or meeting link') }}">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="appointment_notes" class="form-label">{{ __('Notes') }}</label>
                                    <textarea class="form-control" id="appointment_notes" name="notes" rows="3" placeholder="{{ __('Additional notes for the appointment') }}"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-purple" onclick="submitAppointmentBookingForm()">{{ __('Book Appointment') }}</button>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Custom button colors */
        .btn-purple {
            background-color: #6f42c1;
            border-color: #6f42c1;
            color: white;
        }

        .btn-purple:hover {
            background-color: #5a359a;
            border-color: #5a359a;
            color: white;
        }

        /* Action buttons styling */
        #actionButtonsContainer {
            animation: slideIn 0.3s ease-in-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-10px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Enhanced filter styling */
        .offcanvas-body .form-group {
            margin-bottom: 1rem;
        }

        .offcanvas-body .form-group label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        /* Contact selection styling */
        .contact-checkbox {
            transform: scale(1.2);
        }

        .contact-checkbox:checked {
            background-color: #007bff;
            border-color: #007bff;
        }

        /* Selected contacts display */
        .badge {
            font-size: 0.875em;
        }

        /* Modal improvements */
        .modal-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .modal-footer {
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }

        /* Progress bar styling */
        .progress {
            height: 1.5rem;
            background-color: #e9ecef;
        }

        .progress-bar {
            background-color: #007bff;
            transition: width 0.3s ease;
        }

        /* Alert styling */
        .alert {
            border-radius: 0.5rem;
            border: none;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
        }

        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        /* Tooltip improvements */
        .tooltip {
            font-size: 0.875rem;
        }

        /* Button group spacing */
        .btn-group .btn {
            margin-right: 0;
        }

        .btn-group .btn + .btn {
            margin-left: -1px;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            #actionButtonsContainer .btn-group {
                flex-direction: column;
            }

            #actionButtonsContainer .btn {
                margin-bottom: 0.25rem;
                border-radius: 0.375rem !important;
            }

            .offcanvas {
                width: 100% !important;
            }
        }

    <!-- Bulk Import Modal -->
    <div class="modal fade" id="bulkImportModal" tabindex="-1" aria-labelledby="bulkImportModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkImportModalLabel">{{ __('Bulk Import Contacts') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h6>{{ __('Import Instructions:') }}</h6>
                                <ul class="mb-0">
                                    <li>{{ __('Download the sample CSV file to see the required format') }}</li>
                                    <li>{{ __('Supported columns: Name, Email, Phone, Type (Lead/Deal), Pipeline, Stage') }}</li>
                                    <li>{{ __('Name is required, other fields are optional') }}</li>
                                    <li>{{ __('Maximum file size: 5MB') }}</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="text-center mb-3">
                                <a href="{{ route('contacts.sample-import') }}" class="btn btn-outline-primary">
                                    <i class="fa fa-download"></i> {{ __('Download Sample CSV') }}
                                </a>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <form id="bulkImportForm" enctype="multipart/form-data">
                                @csrf
                                <div class="form-group mb-3">
                                    <label for="import_file" class="form-label">{{ __('Select CSV File') }} <span class="text-danger">*</span></label>
                                    <input type="file" class="form-control" id="import_file" name="import_file" accept=".csv" required>
                                    <small class="form-text text-muted">{{ __('Only CSV files are supported') }}</small>
                                </div>
                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="skip_duplicates" name="skip_duplicates" checked>
                                        <label class="form-check-label" for="skip_duplicates">
                                            {{ __('Skip duplicate contacts (based on email)') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group mb-3">
                                    <label for="default_pipeline" class="form-label">{{ __('Default Pipeline (for Leads)') }}</label>
                                    <select class="form-control" id="default_pipeline" name="default_pipeline">
                                        <option value="">{{ __('Select Default Pipeline') }}</option>
                                        @foreach($pipelines as $pipeline)
                                            <option value="{{ $pipeline->id }}">{{ $pipeline->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group mb-3">
                                    <label for="default_stage" class="form-label">{{ __('Default Stage (for Leads)') }}</label>
                                    <select class="form-control" id="default_stage" name="default_stage">
                                        <option value="">{{ __('Select Default Stage') }}</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-12">
                            <div id="importProgress" class="progress" style="display: none;">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="importResults" style="display: none;"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-dark" onclick="submitBulkImportForm()">{{ __('Import Contacts') }}</button>
                </div>
            </div>
        </div>
    </div>

        /* External Contacts Loading Styles */
        .external-loading-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            padding: 2rem;
            margin: 1rem 0;
            position: relative;
            overflow: hidden;
        }

        .external-loading-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .external-loading-content {
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .external-loading-spinner {
            margin-bottom: 1rem;
        }

        .external-loading-spinner .spinner-border {
            width: 2.5rem;
            height: 2.5rem;
            border-width: 0.3em;
            animation: spin 1s linear infinite;
        }

        .external-loading-text {
            font-size: 1.1rem;
            color: #495057;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .external-loading-text i {
            font-size: 1.2rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .external-loading-subtext {
            color: #6c757d;
            font-style: italic;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .external-loading-container {
                padding: 1.5rem;
                margin: 0.5rem 0;
            }

            .external-loading-text {
                font-size: 1rem;
            }

            .external-loading-spinner .spinner-border {
                width: 2rem;
                height: 2rem;
            }
        }
    </style>
@endsection

@push('script-page')
    <script>
        // Pass pipelines data to JavaScript
        const pipelines = @json($pipelines);

        // Initialize tooltips
        $(document).ready(function() {
            // Initialize Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize Select2 for tags
            $('#contact_tags').select2({
                placeholder: '{{ __("Select or create tags") }}',
                allowClear: true,
                tags: true,
                createTag: function(params) {
                    if (params.term.trim() === '') {
                        return undefined;
                    }
                    return {
                        id: 'new_' + params.term,
                        text: params.term,
                        newTag: true
                    };
                }
            });

            // Load initial data
            loadContactGroups();
            loadWorkflows();
            loadTags();
            loadContactsForAppointment();
            loadFilterData();
        });

        // Toggle action buttons visibility
        $('#toggleActionsBtn').on('click', function() {
            const container = $('#actionButtonsContainer');
            const icon = $(this).find('i');

            if (container.hasClass('d-none')) {
                container.removeClass('d-none').addClass('d-inline-flex');
                icon.removeClass('fa-ellipsis-h').addClass('fa-times');
                $(this).attr('title', '{{ __("Hide Actions") }}');
            } else {
                container.removeClass('d-inline-flex').addClass('d-none');
                icon.removeClass('fa-times').addClass('fa-ellipsis-h');
                $(this).attr('title', '{{ __("Toggle Actions") }}');
            }
        });

        // Contact type change handler for add contact modal
        $('#contact_type').on('change', function() {
            const type = $(this).val();
            if (type === 'lead') {
                $('#leadFields').show();
                $('#dealFields').hide();
                $('#new_pipeline_id, #new_stage_id').prop('required', true);
                $('#new_deal_pipeline_id, #new_deal_stage_id').prop('required', false);
            } else if (type === 'deal') {
                $('#leadFields').hide();
                $('#dealFields').show();
                $('#new_pipeline_id, #new_stage_id').prop('required', false);
                $('#new_deal_pipeline_id, #new_deal_stage_id').prop('required', true);
            } else {
                $('#leadFields, #dealFields').hide();
                $('#new_pipeline_id, #new_stage_id, #new_deal_pipeline_id, #new_deal_stage_id').prop('required', false);
            }
        });

        // Pipeline change handlers
        $('#new_pipeline_id').on('change', function() {
            loadStagesForPipeline($(this).val(), '#new_stage_id');
        });

        $('#new_deal_pipeline_id').on('change', function() {
            loadStagesForPipeline($(this).val(), '#new_deal_stage_id');
        });

        $('#default_pipeline').on('change', function() {
            loadStagesForPipeline($(this).val(), '#default_stage');
        });

        // Function to load stages for a pipeline
        function loadStagesForPipeline(pipelineId, targetSelect) {
            const stageSelect = $(targetSelect);
            stageSelect.empty().append('<option value="">{{ __("Select Stage") }}</option>');

            if (pipelineId) {
                stageSelect.append('<option value="">{{ __("Loading stages...") }}</option>');

                $.ajax({
                    url: '/api/get-pipeline-stages',
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: { pipeline_id: pipelineId },
                    success: function(response) {
                        stageSelect.empty().append('<option value="">{{ __("Select Stage") }}</option>');
                        if (response.stages && response.stages.length > 0) {
                            response.stages.forEach(stage => {
                                stageSelect.append(`<option value="${stage.id}">${stage.name}</option>`);
                            });
                        }
                    },
                    error: function() {
                        stageSelect.empty().append('<option value="">{{ __("Error loading stages") }}</option>');
                    }
                });
            }
        }

        // Function to submit add contact form
        function submitAddContactForm() {
            const form = $('#addContactForm')[0];
            const formData = new FormData(form);
            const submitBtn = $('.modal-footer .btn-success');
            const originalText = submitBtn.text();

            submitBtn.html('<i class="fa fa-spinner fa-spin"></i> {{ __("Adding...") }}').prop('disabled', true);

            $.ajax({
                url: form.action,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#addContactModal').modal('hide');
                    showSuccessMessage(response.message || '{{ __("Contact added successfully") }}');
                    setTimeout(() => location.reload(), 1000);
                },
                error: function(xhr) {
                    handleFormErrors(xhr, '#addContactForm');
                },
                complete: function() {
                    submitBtn.text(originalText).prop('disabled', false);
                }
            });
        }

        // Function to load contact groups
        function loadContactGroups() {
            $.ajax({
                url: '/api/contact-groups',
                method: 'GET',
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                success: function(response) {
                    const select = $('#contact_group');
                    select.empty().append('<option value="">{{ __("Select Group") }}</option>');
                    if (response.groups) {
                        response.groups.forEach(group => {
                            select.append(`<option value="${group.id}">${group.name}</option>`);
                        });
                    }
                }
            });
        }

        // Function to load workflows
        function loadWorkflows() {
            $.ajax({
                url: '/api/workflows',
                method: 'GET',
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                success: function(response) {
                    const select = $('#workflow_id');
                    select.empty().append('<option value="">{{ __("Select Workflow") }}</option>');
                    if (response.workflows) {
                        response.workflows.forEach(workflow => {
                            select.append(`<option value="${workflow.id}">${workflow.name}</option>`);
                        });
                    }
                }
            });
        }

        // Function to load tags
        function loadTags() {
            $.ajax({
                url: '/api/contact-tags',
                method: 'GET',
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                success: function(response) {
                    const select = $('#contact_tags');
                    select.empty();
                    if (response.tags) {
                        response.tags.forEach(tag => {
                            select.append(`<option value="${tag.id}">${tag.name}</option>`);
                        });
                    }
                }
            });
        }

        // Function to load contacts for appointment
        function loadContactsForAppointment() {
            $.ajax({
                url: '/api/contacts-list',
                method: 'GET',
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                success: function(response) {
                    const select = $('#appointment_contact');
                    select.empty().append('<option value="">{{ __("Select Contact") }}</option>');
                    if (response.contacts) {
                        response.contacts.forEach(contact => {
                            select.append(`<option value="${contact.id}" data-type="${contact.type}">${contact.name} (${contact.type})</option>`);
                        });
                    }
                }
            });
        }

        // Function to export contacts
        function exportContacts() {
            window.location.href = '{{ route("contacts.export") }}';
        }

        // Function to load filter data
        function loadFilterData() {
            // Load stages for selected pipeline
            const selectedPipeline = $('#filter_pipeline').val();
            if (selectedPipeline) {
                loadStagesForPipeline(selectedPipeline, '#filter_stage');
            }

            // Load tags for filter
            loadTagsForFilter();

            // Load groups for filter
            loadGroupsForFilter();

            // Load workflows for filter
            loadWorkflowsForFilter();
        }

        // Pipeline change handler for filter
        $('#filter_pipeline').on('change', function() {
            const pipelineId = $(this).val();
            loadStagesForPipeline(pipelineId, '#filter_stage');
        });

        // Function to load tags for filter
        function loadTagsForFilter() {
            $.ajax({
                url: '/api/contact-tags',
                method: 'GET',
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                success: function(response) {
                    const select = $('#filter_tags');
                    select.empty();
                    if (response.tags) {
                        response.tags.forEach(tag => {
                            const isSelected = '{{ request("filter_tags") }}'.includes(tag.id.toString());
                            select.append(`<option value="${tag.id}" ${isSelected ? 'selected' : ''}>${tag.name}</option>`);
                        });
                    }
                    // Initialize Select2 for tags filter
                    select.select2({
                        placeholder: '{{ __("Select tags") }}',
                        allowClear: true
                    });
                }
            });
        }

        // Function to load groups for filter
        function loadGroupsForFilter() {
            $.ajax({
                url: '/api/contact-groups',
                method: 'GET',
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                success: function(response) {
                    const select = $('#filter_group');
                    select.empty().append('<option value="">{{ __("All Groups") }}</option>');
                    if (response.groups) {
                        response.groups.forEach(group => {
                            const isSelected = '{{ request("filter_group") }}' == group.id;
                            select.append(`<option value="${group.id}" ${isSelected ? 'selected' : ''}>${group.name}</option>`);
                        });
                    }
                }
            });
        }

        // Function to load workflows for filter
        function loadWorkflowsForFilter() {
            $.ajax({
                url: '/api/workflows',
                method: 'GET',
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                success: function(response) {
                    const select = $('#filter_workflow');
                    select.empty().append('<option value="">{{ __("All Workflows") }}</option>');
                    if (response.workflows) {
                        response.workflows.forEach(workflow => {
                            const isSelected = '{{ request("filter_workflow") }}' == workflow.id;
                            select.append(`<option value="${workflow.id}" ${isSelected ? 'selected' : ''}>${workflow.name}</option>`);
                        });
                    }
                }
            });
        }

        // Enhanced filter form submission
        $('#contactsFilterForm').on('submit', function(e) {
            e.preventDefault();

            // Get all form data
            const formData = $(this).serialize();

            // Redirect with query parameters
            window.location.href = '{{ route("contacts.index") }}?' + formData;
        });

        // Quick filter buttons
        function applyQuickFilter(filterType, filterValue) {
            const url = new URL(window.location.href);
            url.searchParams.set(filterType, filterValue);
            window.location.href = url.toString();
        }

        // Clear all filters
        function clearAllFilters() {
            window.location.href = '{{ route("contacts.index") }}';
        }

        // Helper function to show success message
        function showSuccessMessage(message) {
            $('body').append(`
                <div class="alert alert-success alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                    <strong>{{ __('Success!') }}</strong> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);
            setTimeout(() => $('.alert-success').fadeOut(), 3000);
        }

        // Helper function to handle form errors
        function handleFormErrors(xhr, formSelector) {
            const errors = xhr.responseJSON?.errors || {};
            let errorHtml = '<div class="alert alert-danger"><ul class="mb-0">';

            if (Object.keys(errors).length > 0) {
                Object.values(errors).forEach(error => {
                    errorHtml += `<li>${error[0]}</li>`;
                });
            } else {
                errorHtml += '<li>{{ __("An error occurred") }}</li>';
            }

            errorHtml += '</ul></div>';
            $(formSelector).prepend(errorHtml);
        }

        // Functions for other modals
        function submitAddToGroupForm() {
            const formData = $('#addToGroupForm').serialize();
            const submitBtn = $('.modal-footer .btn-info');
            const originalText = submitBtn.text();

            submitBtn.html('<i class="fa fa-spinner fa-spin"></i> {{ __("Adding...") }}').prop('disabled', true);

            $.ajax({
                url: '/api/contacts/add-to-group',
                method: 'POST',
                data: formData,
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                success: function(response) {
                    $('#addToGroupModal').modal('hide');
                    showSuccessMessage(response.message || '{{ __("Contacts added to group successfully") }}');
                },
                error: function(xhr) {
                    handleFormErrors(xhr, '#addToGroupForm');
                },
                complete: function() {
                    submitBtn.text(originalText).prop('disabled', false);
                }
            });
        }

        function submitAddToWorkflowForm() {
            const formData = $('#addToWorkflowForm').serialize();
            const submitBtn = $('.modal-footer .btn-warning');
            const originalText = submitBtn.text();

            submitBtn.html('<i class="fa fa-spinner fa-spin"></i> {{ __("Adding...") }}').prop('disabled', true);

            $.ajax({
                url: '/api/contacts/add-to-workflow',
                method: 'POST',
                data: formData,
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                success: function(response) {
                    $('#addToWorkflowModal').modal('hide');
                    showSuccessMessage(response.message || '{{ __("Contacts added to workflow successfully") }}');
                },
                error: function(xhr) {
                    handleFormErrors(xhr, '#addToWorkflowForm');
                },
                complete: function() {
                    submitBtn.text(originalText).prop('disabled', false);
                }
            });
        }

        function submitAddTagForm() {
            const formData = $('#addTagForm').serialize();
            const submitBtn = $('.modal-footer .btn-primary');
            const originalText = submitBtn.text();

            submitBtn.html('<i class="fa fa-spinner fa-spin"></i> {{ __("Adding...") }}').prop('disabled', true);

            $.ajax({
                url: '/api/contacts/add-tags',
                method: 'POST',
                data: formData,
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                success: function(response) {
                    $('#addTagModal').modal('hide');
                    showSuccessMessage(response.message || '{{ __("Tags added successfully") }}');
                },
                error: function(xhr) {
                    handleFormErrors(xhr, '#addTagForm');
                },
                complete: function() {
                    submitBtn.text(originalText).prop('disabled', false);
                }
            });
        }

        function submitAppointmentBookingForm() {
            const formData = $('#appointmentBookingForm').serialize();
            const submitBtn = $('.modal-footer .btn-purple');
            const originalText = submitBtn.text();

            submitBtn.html('<i class="fa fa-spinner fa-spin"></i> {{ __("Booking...") }}').prop('disabled', true);

            $.ajax({
                url: '/api/appointments/book',
                method: 'POST',
                data: formData,
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                success: function(response) {
                    $('#appointmentBookingModal').modal('hide');
                    showSuccessMessage(response.message || '{{ __("Appointment booked successfully") }}');
                },
                error: function(xhr) {
                    handleFormErrors(xhr, '#appointmentBookingForm');
                },
                complete: function() {
                    submitBtn.text(originalText).prop('disabled', false);
                }
            });
        }

        function submitBulkImportForm() {
            const form = $('#bulkImportForm')[0];
            const formData = new FormData(form);
            const submitBtn = $('.modal-footer .btn-dark');
            const originalText = submitBtn.text();

            // Validate file selection
            if (!$('#import_file')[0].files.length) {
                alert('{{ __("Please select a CSV file") }}');
                return;
            }

            submitBtn.html('<i class="fa fa-spinner fa-spin"></i> {{ __("Importing...") }}').prop('disabled', true);
            $('#importProgress').show();

            $.ajax({
                url: '/api/contacts/bulk-import',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                xhr: function() {
                    const xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener("progress", function(evt) {
                        if (evt.lengthComputable) {
                            const percentComplete = evt.loaded / evt.total * 100;
                            $('.progress-bar').css('width', percentComplete + '%');
                        }
                    }, false);
                    return xhr;
                },
                success: function(response) {
                    $('#importResults').html(`
                        <div class="alert alert-success">
                            <h6>{{ __('Import Completed') }}</h6>
                            <p>{{ __('Successfully imported') }}: ${response.imported || 0}</p>
                            <p>{{ __('Skipped duplicates') }}: ${response.skipped || 0}</p>
                            <p>{{ __('Errors') }}: ${response.errors || 0}</p>
                        </div>
                    `).show();
                    setTimeout(() => {
                        $('#bulkImportModal').modal('hide');
                        location.reload();
                    }, 3000);
                },
                error: function(xhr) {
                    $('#importResults').html(`
                        <div class="alert alert-danger">
                            <h6>{{ __('Import Failed') }}</h6>
                            <p>${xhr.responseJSON?.message || '{{ __("An error occurred during import") }}'}</p>
                        </div>
                    `).show();
                },
                complete: function() {
                    submitBtn.text(originalText).prop('disabled', false);
                    $('#importProgress').hide();
                }
            });
        }

        // Contact selection functionality for group operations
        let selectedContacts = [];

        function toggleContactSelection(contactId, contactName, contactType) {
            const index = selectedContacts.findIndex(c => c.id === contactId);
            if (index > -1) {
                selectedContacts.splice(index, 1);
            } else {
                selectedContacts.push({ id: contactId, name: contactName, type: contactType });
            }
            updateSelectedContactsDisplay();
        }

        function updateSelectedContactsDisplay() {
            const containers = ['#selectedContactsList', '#workflowSelectedContactsList', '#tagSelectedContactsList'];

            containers.forEach(container => {
                const element = $(container);
                if (selectedContacts.length === 0) {
                    element.html('<small class="text-muted">{{ __("Please select contacts from the table first") }}</small>');
                } else {
                    let html = '';
                    selectedContacts.forEach(contact => {
                        html += `<span class="badge bg-primary me-1 mb-1">${contact.name} (${contact.type})</span>`;
                    });
                    element.html(html);
                }
            });
        }

        // Add checkboxes to contact table for selection
        $(document).ready(function() {
            // Add header checkbox
            $('#contacts-table thead tr').prepend('<th class="text-center"><input type="checkbox" id="selectAllContacts"></th>');

            // Add checkboxes to each row
            $('#contacts-table tbody tr').each(function() {
                const row = $(this);
                if (!row.hasClass('external-contact')) {
                    // Extract contact info from existing row
                    const nameElement = row.find('strong').first();
                    const contactName = nameElement.text().trim();
                    const typeElement = row.find('.badge').first();
                    const contactType = typeElement.text().trim();

                    // Generate a unique ID for internal contacts
                    const contactId = `${contactType.toLowerCase()}_${Math.random().toString(36).substr(2, 9)}`;

                    row.prepend(`<td class="text-center"><input type="checkbox" class="contact-checkbox" data-contact-id="${contactId}" data-contact-name="${contactName}" data-contact-type="${contactType}"></td>`);
                }
            });

            // Handle select all
            $('#selectAllContacts').on('change', function() {
                const isChecked = $(this).is(':checked');
                $('.contact-checkbox').prop('checked', isChecked);

                if (isChecked) {
                    selectedContacts = [];
                    $('.contact-checkbox').each(function() {
                        const checkbox = $(this);
                        selectedContacts.push({
                            id: checkbox.data('contact-id'),
                            name: checkbox.data('contact-name'),
                            type: checkbox.data('contact-type')
                        });
                    });
                } else {
                    selectedContacts = [];
                }
                updateSelectedContactsDisplay();
            });

            // Handle individual checkbox changes
            $(document).on('change', '.contact-checkbox', function() {
                const checkbox = $(this);
                const contactId = checkbox.data('contact-id');
                const contactName = checkbox.data('contact-name');
                const contactType = checkbox.data('contact-type');

                toggleContactSelection(contactId, contactName, contactType);

                // Update select all checkbox
                const totalCheckboxes = $('.contact-checkbox').length;
                const checkedCheckboxes = $('.contact-checkbox:checked').length;
                $('#selectAllContacts').prop('checked', totalCheckboxes === checkedCheckboxes);
            });
        });
    </script>
    <script>
        function editContact(id, type) {
            // Get current contact data from the table row
            let row = $(`button[onclick="editContact('${id}', '${type}')"]`).closest('tr');
            let name = row.find('strong').text();
            let email = row.find('i.fa-envelope').parent().text().replace('✉', '').trim();
            let phone = row.find('i.fa-phone').parent().text().replace('📞', '').trim();

            // Create form based on type
            let formHtml = '';
            let actionUrl = '';

            if (type === 'lead') {
                actionUrl = `/contacts/leads/${id}`;
                formHtml = `
                    <form id="editContactForm" action="${actionUrl}" method="POST">
                        @csrf
                        <input type="hidden" name="_method" value="PUT">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="name">{{ __('Name') }} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="name" value="${name}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email">{{ __('Email') }}</label>
                                    <input type="email" class="form-control" name="email" value="${email}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone">{{ __('Phone') }}</label>
                                    <input type="text" class="form-control" name="phone" value="${phone}">
                                </div>
                            </div>
                        </div>
                    </form>
                `;
            } else {
                actionUrl = `/contacts/deals/${id}`;
                formHtml = `
                    <form id="editContactForm" action="${actionUrl}" method="POST">
                        @csrf
                        <input type="hidden" name="_method" value="PUT">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="name">{{ __('Name') }} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="name" value="${name}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone">{{ __('Phone') }}</label>
                                    <input type="text" class="form-control" name="phone" value="${phone}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="price">{{ __('Price') }}</label>
                                    <input type="number" class="form-control" name="price" step="0.01">
                                </div>
                            </div>
                        </div>
                    </form>
                `;
            }

            // Set modal content
            $('#editContactForm').html(formHtml);

            // Add modal footer with buttons
            let modalFooter = `
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-primary" onclick="updateContact('${type}')">{{ __('Update') }}</button>
                </div>
            `;

            $('.modal-content').find('.modal-footer').remove();
            $('.modal-content').append(modalFooter);

            // Show modal
            $('#editContactModal').modal('show');
        }

        function updateContact(type) {
            let form = $('#editContactForm form')[0];
            let formData = new FormData(form);

            // Show loading on submit button
            let submitBtn = $('.modal-footer .btn-primary');
            let originalText = submitBtn.text();
            submitBtn.html('<i class="fa fa-spinner fa-spin"></i> {{ __("Updating...") }}').prop('disabled', true);

            // Clear previous errors
            $('.alert-danger').remove();

            $.ajax({
                url: form.action,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#editContactModal').modal('hide');

                    // Show success message
                    $('body').append(`
                        <div class="alert alert-success alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                            <strong>{{ __('Success!') }}</strong> ${response.success}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    `);

                    // Auto hide success message
                    setTimeout(function() {
                        $('.alert-success').fadeOut();
                    }, 3000);

                    // Reload the page to show updated data
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                },
                error: function(xhr) {
                    let errors = xhr.responseJSON?.errors || {};
                    let errorHtml = '<div class="alert alert-danger"><ul class="mb-0">';

                    if (Object.keys(errors).length > 0) {
                        Object.values(errors).forEach(function(error) {
                            errorHtml += `<li>${error[0]}</li>`;
                        });
                    } else {
                        errorHtml += '<li>{{ __("An error occurred while updating") }}</li>';
                    }

                    errorHtml += '</ul></div>';

                    $('#editContactForm').prepend(errorHtml);

                    // Reset submit button
                    submitBtn.text(originalText).prop('disabled', false);
                }
            });
        }

        function deleteContact(id, type, name) {
            // Set the message with contact name and type
            let message = `{{ __('Are you sure you want to delete') }} "${name}"?<br>`;
            message += `<small class="text-muted">{{ __('This') }} ${type} {{ __('will be permanently removed from the system.') }}</small>`;

            $('#deleteContactMessage').html(message);

            // Set up the confirm button click handler
            $('#confirmDeleteBtn').off('click').on('click', function() {
                let deleteUrl = '';
                if (type === 'lead') {
                    deleteUrl = `/leads/${id}`;
                } else {
                    deleteUrl = `/deals/${id}`;
                }

                // Show loading on delete button
                let deleteBtn = $(this);
                let originalText = deleteBtn.text();
                deleteBtn.html('<i class="fa fa-spinner fa-spin"></i> {{ __("Deleting...") }}').prop('disabled', true);

                // Create form and submit
                let form = $('<form>', {
                    'method': 'POST',
                    'action': deleteUrl
                });

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_token',
                    'value': '{{ csrf_token() }}'
                }));

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_method',
                    'value': 'DELETE'
                }));

                // Submit via AJAX
                $.ajax({
                    url: deleteUrl,
                    method: 'POST',
                    data: form.serialize(),
                    success: function(response) {
                        $('#deleteContactModal').modal('hide');

                        // Show success message
                        $('body').append(`
                            <div class="alert alert-success alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                                <strong>{{ __('Success!') }}</strong> ${type} {{ __('deleted successfully') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        `);

                        // Auto hide success message
                        setTimeout(function() {
                            $('.alert-success').fadeOut();
                        }, 3000);

                        // Reload the page to show updated data
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    },
                    error: function(xhr) {
                        $('#deleteContactModal').modal('hide');

                        // Show error message
                        $('body').append(`
                            <div class="alert alert-danger alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                                <strong>{{ __('Error!') }}</strong> {{ __('Failed to delete') }} ${type}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        `);

                        // Auto hide error message
                        setTimeout(function() {
                            $('.alert-danger').fadeOut();
                        }, 5000);
                    },
                    complete: function() {
                        // Reset delete button
                        deleteBtn.text(originalText).prop('disabled', false);
                    }
                });
            });

            // Show the modal
            $('#deleteContactModal').modal('show');
        }

        // Function to get proper SSO token from backend (same as leads)
        function getSsoToken() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: '/api/generate-sso-token',
                    type: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success && response.token) {
                            resolve(response.token);
                        } else {
                            reject('Failed to generate SSO token');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('SSO Token generation failed:', xhr.responseJSON);
                        reject('Failed to generate SSO token: ' + error);
                    }
                });
            });
        }

        // Variable to prevent multiple simultaneous loads
        let isLoadingExternalContacts = false;

        // Function to load and display external contacts in the table
        function loadExternalContacts() {
            // Prevent multiple simultaneous loads
            if (isLoadingExternalContacts) {
                return;
            }

            isLoadingExternalContacts = true;
            $('#loading-external').show();

            @if($omxFlowModule)
            const moduleBaseUrl = '{{ $omxFlowModule->base_url }}';
            @else
            const moduleBaseUrl = config('app.url');
            @endif

            getSsoToken().then(function(ssoToken) {
                $.ajax({
                    url: moduleBaseUrl + 'api/sso/contacts/simple-list',
                    type: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + ssoToken,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-User-Email': '{{ Auth::user()->email }}'
                    },
                    success: function(response) {
                        $('#loading-external').hide();
                        isLoadingExternalContacts = false;

                        if (response.data && response.data.contacts && response.data.contacts.length > 0) {
                            // Always remove existing external contacts first
                            $('.external-contact').remove();

                            // Filter out contacts that are already converted to leads
                            filterAndAddExternalContacts(response.data.contacts);
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#loading-external').hide();
                        isLoadingExternalContacts = false;
                        console.error('Failed to load external contacts:', error);

                        if (xhr.status !== 0) { // Don't show error if it's just a connection issue
                            console.warn('Could not load external contacts. External service may be unavailable.');
                        }
                    }
                });
            }).catch(function(error) {
                $('#loading-external').hide();
                isLoadingExternalContacts = false;
                console.error('Failed to generate SSO token for external contacts:', error);
            });
        }

        // Function to add external contact to table
        function addExternalContactToTable(contact) {
            const contactName = contact.full_name || contact.name || 'N/A';
            const contactEmail = contact.email || 'N/A';
            const contactPhone = contact.phone || contact.mobile || contact.phone_number || 'N/A';
            const externalId = contact.id;

            // Check if this contact is already in the table (prevent duplicates)
            const existingRow = $(`.external-contact[data-external-id="${externalId}"]`);
            if (existingRow.length > 0) {
                return; // Contact already exists, don't add duplicate
            }

            const row = `
                <tr class="external-contact" data-source="external" data-external-id="${externalId}" data-email="${contactEmail.toLowerCase()}">
                    <td class="text-center">
                        <div>
                            <strong>${contactName}</strong><br>
                            ${contactEmail !== 'N/A' ? `<small class="text-muted"><i class="fa fa-envelope"></i> ${contactEmail}</small><br>` : ''}
                            ${contactPhone !== 'N/A' ? `<small class="text-muted"><i class="fa fa-phone"></i> ${contactPhone}</small>` : ''}
                        </div>
                    </td>
                    <td class="text-center">
                        <span class="badge bg-warning">
                            <i class="fab fa-whatsapp"></i> External Contact
                        </span>
                    </td>
                    <td class="text-center">
                        <div class="action-btn me-2">
                            <button class="btn btn-sm align-items-center bg-primary convert-to-lead-btn"
                                    data-external-id="${externalId}"
                                    data-contact-name="${contactName}"
                                    data-contact-email="${contactEmail}"
                                    data-contact-phone="${contactPhone}"
                                    data-bs-toggle="tooltip"
                                    title="{{ __('Convert to Lead') }}">
                                <i class="ti ti-user-plus text-white"></i> {{ __('Convert to Lead') }}
                            </button>
                        </div>
                    </td>
                </tr>
            `;

            $('#contacts-table tbody').append(row);
        }

        // Function to show convert to lead modal
        function showConvertToLeadModal(externalId, contactName, contactEmail, contactPhone, button) {
            // Store the button reference for later use
            window.currentConvertButton = button;

            console.log('Opening modal for:', {externalId, contactName, contactEmail, contactPhone});
            console.log('Available pipelines:', pipelines);

            // Populate modal fields
            $('#contact_name').val(contactName);
            $('#contact_email').val(contactEmail);
            $('#contact_phone').val(contactPhone || 'N/A');
            $('#external_id').val(externalId);

            // Clear and populate pipeline dropdown
            const pipelineSelect = $('#pipeline_id');
            pipelineSelect.empty().append('<option value="">{{ __("Select Pipeline") }}</option>');

            if (pipelines && pipelines.length > 0) {
                pipelines.forEach(pipeline => {
                    console.log('Adding pipeline:', pipeline);
                    pipelineSelect.append(`<option value="${pipeline.id}">${pipeline.name}</option>`);
                });
            } else {
                console.log('No pipelines available');
                pipelineSelect.append('<option value="">{{ __("No pipelines available") }}</option>');
            }

            // Clear stage dropdown
            $('#stage_id').empty().append('<option value="">{{ __("Select Stage") }}</option>');

            // Show the modal
            $('#convertToLeadModal').modal('show');
        }

        // Handle pipeline selection change
        $(document).on('change', '#pipeline_id', function() {
            const pipelineId = $(this).val();
            const stageSelect = $('#stage_id');

            console.log('Pipeline selected:', pipelineId);

            // Clear stage dropdown
            stageSelect.empty().append('<option value="">{{ __("Select Stage") }}</option>');

            if (pipelineId) {
                // Show loading in stage dropdown
                stageSelect.append('<option value="">{{ __("Loading stages...") }}</option>');

                // Fetch stages for selected pipeline
                $.ajax({
                    url: '/api/get-pipeline-stages',
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: {
                        pipeline_id: pipelineId
                    },
                    success: function(response) {
                        console.log('Stages response:', response);

                        // Clear loading option
                        stageSelect.empty().append('<option value="">{{ __("Select Stage") }}</option>');

                        if (response.stages && response.stages.length > 0) {
                            response.stages.forEach(stage => {
                                console.log('Adding stage:', stage);
                                stageSelect.append(`<option value="${stage.id}">${stage.name}</option>`);
                            });
                        } else {
                            stageSelect.append('<option value="">{{ __("No stages found") }}</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Failed to fetch stages:', error);
                        console.error('XHR response:', xhr.responseJSON);

                        // Clear loading option and show error
                        stageSelect.empty().append('<option value="">{{ __("Error loading stages") }}</option>');
                    }
                });
            }
        });

        // Handle convert confirmation
        $(document).on('click', '#confirmConvertBtn', function() {
            const form = $('#convertToLeadForm');
            const pipelineId = $('#pipeline_id').val();
            const stageId = $('#stage_id').val();

            // Validate required fields
            if (!pipelineId || !stageId) {
                alert('{{ __("Please select both pipeline and stage") }}');
                return;
            }

            // Get form data
            const contactPhone = $('#contact_phone').val();
            const formData = {
                name: $('#contact_name').val(),
                email: $('#contact_email').val(),
                phone: contactPhone !== 'N/A' ? contactPhone : '',
                subject: 'Converted from External Contact',
                pipeline_id: pipelineId,
                stage_id: stageId,
                user_id: '{{ Auth::user()->id }}',
                _token: $('meta[name="csrf-token"]').attr('content')
            };

            // Perform the conversion
            convertExternalContactToLeadWithData(formData, window.currentConvertButton);
        });

        // Function to filter external contacts and check against existing leads
        function filterAndAddExternalContacts(externalContacts) {
            // Get existing lead emails from the current contacts table
            const existingLeadEmails = new Set();

            // Collect emails from server-side rendered contacts (leads and deals)
            $('#contacts-table tbody tr:not(.external-contact)').each(function() {
                const emailElement = $(this).find('i.fa-envelope').parent();
                if (emailElement.length > 0) {
                    const emailText = emailElement.text().trim();
                    if (emailText) {
                        existingLeadEmails.add(emailText.toLowerCase());
                    }
                }
            });

            // Get emails from external contacts to check
            const externalEmails = externalContacts.map(contact => contact.email).filter(email => email);

            // If no external contacts have emails, just add them all
            if (externalEmails.length === 0) {
                externalContacts.forEach(contact => {
                    addExternalContactToTable(contact);
                });
                return;
            }

            // Make AJAX call to get all existing leads to check for converted contacts
            $.ajax({
                url: '/api/check-converted-contacts',
                type: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                data: JSON.stringify({
                    emails: externalEmails
                }),
                success: function(response) {
                    const convertedEmails = new Set(response.converted_emails || []);

                    // Use a Set to track unique contacts by email to prevent duplicates
                    const uniqueContacts = new Map();

                    // Filter out contacts that are already converted or exist as leads
                    externalContacts.forEach(contact => {
                        const contactEmail = (contact.email || '').toLowerCase();
                        const contactName = contact.full_name || contact.name || 'N/A';

                        // Only add if email exists and not already converted/existing
                        if (contactEmail &&
                            !existingLeadEmails.has(contactEmail) &&
                            !convertedEmails.has(contactEmail)) {

                            // Use email as unique key to prevent duplicates
                            uniqueContacts.set(contactEmail, contact);
                        }
                    });

                    // Add unique filtered contacts to table
                    uniqueContacts.forEach(contact => {
                        addExternalContactToTable(contact);
                    });

                    // Apply current filters to newly added external contacts
                    setTimeout(function() {
                        applyFiltersToAllContacts();
                    }, 100);
                },
                error: function(xhr, status, error) {
                    console.error('Failed to check converted contacts:', error);
                    // If the check fails, show all contacts (fallback)
                    externalContacts.forEach(contact => {
                        addExternalContactToTable(contact);
                    });

                    // Apply current filters to newly added external contacts
                    setTimeout(function() {
                        applyFiltersToAllContacts();
                    }, 100);
                }
            });
        }

        // Function to convert external contact to lead with form data
        function convertExternalContactToLeadWithData(leadData, button) {
            const originalHtml = button.html();
            button.prop('disabled', true).html('<i class="ti ti-loader ti-spin"></i> Converting...');

            // Make AJAX call to create new lead
            $.ajax({
                url: '{{ route("leads.store") }}',
                type: 'POST',
                data: leadData,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    // Hide the modal
                    $('#convertToLeadModal').modal('hide');

                    // Remove the external contact row
                    const externalRow = button.closest('tr');
                    externalRow.remove();

                    // Show success message
                    if (typeof show_toastr === 'function') {
                        show_toastr('success', `${leadData.name} has been successfully converted to a lead!`, 'success');
                    } else {
                        alert(`${leadData.name} has been successfully converted to a lead!`);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Conversion failed:', xhr.responseJSON);
                    let errorMessage = 'Failed to convert contact to lead';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                        const errors = Object.values(xhr.responseJSON.errors).flat();
                        errorMessage = errors.join(', ');
                    }

                    // Show error message
                    if (typeof show_toastr === 'function') {
                        show_toastr('error', errorMessage, 'error');
                    } else {
                        alert('Error: ' + errorMessage);
                    }
                },
                complete: function() {
                    // Reset button state
                    button.prop('disabled', false).html(originalHtml);
                }
            });
        }



        // Convert to lead button click handler
        $(document).on("click", ".convert-to-lead-btn", function (e) {
            e.preventDefault();
            const externalId = $(this).data('external-id');
            const contactName = $(this).data('contact-name');
            const contactEmail = $(this).data('contact-email');
            const contactPhone = $(this).data('contact-phone');

            // Show the convert to lead modal
            showConvertToLeadModal(externalId, contactName, contactEmail, contactPhone, $(this));
        });

        // Function to apply filters to all contacts (both server-side and external)
        function applyFiltersToAllContacts() {
            const nameFilter = $('#filter_name').val().toLowerCase();
            const emailFilter = $('#filter_email').val().toLowerCase();
            const phoneFilter = $('#filter_phone').val().toLowerCase();
            const typeFilter = $('#filter_type').val();

            // Handle server-side contacts (leads and deals)
            $('tbody tr:not(.external-contact)').each(function() {
                const row = $(this);
                let showRow = true;

                // Get the contact type from the badge in the Type column
                const typeBadge = row.find('td:nth-child(2) .badge').text().trim();
                const contactText = row.text().toLowerCase();

                // Apply type filter
                if (typeFilter) {
                    if (typeFilter === 'WhatsApp') {
                        // If External Contact is selected, hide all server-side contacts
                        showRow = false;
                    } else if (typeFilter === 'Lead' && typeBadge !== 'Lead') {
                        // If Lead is selected, show only leads
                        showRow = false;
                    } else if (typeFilter === 'Deal' && typeBadge !== 'Deal') {
                        // If Deal is selected, show only deals
                        showRow = false;
                    }
                }

                // Apply other filters only if type filter passes
                if (showRow) {
                    if (nameFilter && !contactText.includes(nameFilter)) {
                        showRow = false;
                    }
                    if (emailFilter && !contactText.includes(emailFilter)) {
                        showRow = false;
                    }
                    if (phoneFilter && !contactText.includes(phoneFilter)) {
                        showRow = false;
                    }
                }

                if (showRow) {
                    row.show();
                } else {
                    row.hide();
                }
            });

            // Handle external contacts
            $('.external-contact').each(function() {
                const row = $(this);
                const contactText = row.find('td:first').text().toLowerCase();

                let showRow = true;

                // Apply type filter first - external contacts should only show when WhatsApp is selected or no filter
                if (typeFilter) {
                    if (typeFilter === 'WhatsApp') {
                        // Show external contacts when External Contact is selected
                        showRow = true;
                    } else {
                        // Hide external contacts when Lead or Deal is selected
                        showRow = false;
                    }
                }

                // Apply other filters only if type filter passes
                if (showRow) {
                    if (nameFilter && !contactText.includes(nameFilter)) {
                        showRow = false;
                    }
                    if (emailFilter && !contactText.includes(emailFilter)) {
                        showRow = false;
                    }
                    if (phoneFilter && !contactText.includes(phoneFilter)) {
                        showRow = false;
                    }
                }

                // Show/hide row based on filters
                if (showRow) {
                    row.show();
                } else {
                    row.hide();
                }
            });
        }

        // Handle filter form submission
        $(document).on('submit', '#contactsFilterForm', function(e) {
            // Prevent default form submission for client-side filtering
            e.preventDefault();

            // Apply filters to all contacts (both server-side and external)
            applyFiltersToAllContacts();
        });

        // Handle filter input changes for real-time filtering
        $(document).on('input change', '#filter_name, #filter_email, #filter_phone, #filter_type', function() {
            applyFiltersToAllContacts();
        });

        // Load external contacts when page loads
        $(document).ready(function() {
            // Load external contacts after a short delay to ensure page is fully loaded
            setTimeout(function() {
                // Always load external contacts if none exist
                if ($('.external-contact').length === 0) {
                    loadExternalContacts();
                }

                // Apply filters to external contacts after they load
                setTimeout(function() {
                    applyFiltersToAllContacts();
                }, 2000);
            }, 1000);
        });
    </script>
@endpush
